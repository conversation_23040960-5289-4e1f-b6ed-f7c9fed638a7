/**
 * 包裹初始化数据验证工具
 */

export interface PackageInitializationItem {
  package_code: string
  warehouse_id: string
  sku: string
  clothing_name: string
  original_quantity: number
  current_quantity: number
  location_code?: string
  clothing_id?: string
  oem_clothing_id?: string
  supplier?: string
  transportation_id?: string
  series_number?: number
  inbound_date?: string
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

export interface ValidationError {
  row: number
  field: string
  value: any
  message: string
}

export interface ValidationWarning {
  row: number
  field: string
  value: any
  message: string
}

/**
 * 验证包裹初始化数据（支持混合包裹）
 */
export function validatePackageInitializationData(
  data: PackageInitializationItem[]
): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationWarning[] = []

  // 按包裹编码分组，用于混合包裹验证
  const packageGroups = new Map<string, { items: PackageInitializationItem[], rows: number[] }>()

  data.forEach((item, index) => {
    const row = index + 1
    const packageCode = item.package_code

    if (!packageGroups.has(packageCode)) {
      packageGroups.set(packageCode, { items: [], rows: [] })
    }
    packageGroups.get(packageCode)!.items.push(item)
    packageGroups.get(packageCode)!.rows.push(row)
  })

  // 验证每个包裹组
  for (const [packageCode, group] of packageGroups) {
    const { items, rows } = group

    // 验证包裹级别的一致性（混合包裹的包裹级别信息应该一致）
    if (items.length > 1) {
      validateMixedPackageConsistency(packageCode, items, rows, errors, warnings)
    }

    // 验证每个产品项
    items.forEach((item, itemIndex) => {
      const row = rows[itemIndex]
      validateSingleItem(item, row, errors, warnings)
    })
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 验证混合包裹的一致性
 */
function validateMixedPackageConsistency(
  packageCode: string,
  items: PackageInitializationItem[],
  rows: number[],
  errors: ValidationError[],
  warnings: ValidationWarning[]
) {
  const firstItem = items[0]

  // 检查包裹级别信息的一致性
  const packageLevelFields = ['warehouse_id', 'location_code', 'supplier', 'transportation_id', 'series_number', 'inbound_date']

  for (let i = 1; i < items.length; i++) {
    const currentItem = items[i]
    const currentRow = rows[i]

    packageLevelFields.forEach(field => {
      const firstValue = (firstItem as any)[field]
      const currentValue = (currentItem as any)[field]

      // 对于可选字段，允许空值不一致，但非空值必须一致
      if (firstValue && currentValue && firstValue !== currentValue) {
        warnings.push({
          row: currentRow,
          field: field,
          value: currentValue,
          message: `混合包裹 ${packageCode} 的 ${field} 不一致，建议保持一致`
        })
      }
    })
  }

  // 检查SKU重复（同一包裹内不应有重复的SKU）
  const skuSet = new Set<string>()
  items.forEach((item, itemIndex) => {
    if (skuSet.has(item.sku)) {
      errors.push({
        row: rows[itemIndex],
        field: 'sku',
        value: item.sku,
        message: `混合包裹 ${packageCode} 中存在重复的SKU`
      })
    } else {
      skuSet.add(item.sku)
    }
  })

  // 混合包裹警告
  if (items.length > 5) {
    warnings.push({
      row: rows[0],
      field: 'package_code',
      value: packageCode,
      message: `包裹 ${packageCode} 包含 ${items.length} 个产品，建议控制在5个以内`
    })
  }
}

/**
 * 验证单个产品项
 */
function validateSingleItem(
  item: PackageInitializationItem,
  row: number,
  errors: ValidationError[],
  warnings: ValidationWarning[]
) {
  // 必填字段验证
  if (!item.package_code || item.package_code.trim() === '') {
    errors.push({
      row,
      field: 'package_code',
      value: item.package_code,
      message: '包裹编码不能为空'
    })
  } else {
    // 包裹编码格式验证
    if (!/^[A-Z0-9_-]+$/i.test(item.package_code)) {
      warnings.push({
        row,
        field: 'package_code',
        value: item.package_code,
        message: '包裹编码建议使用字母、数字、下划线或连字符'
      })
    }
  }

  if (!item.warehouse_id || item.warehouse_id.trim() === '') {
    errors.push({
      row,
      field: 'warehouse_id',
      value: item.warehouse_id,
      message: '仓库ID不能为空'
    })
  }

  if (!item.sku || item.sku.trim() === '') {
    errors.push({
      row,
      field: 'sku',
      value: item.sku,
      message: 'SKU不能为空'
    })
  }

  if (!item.clothing_name || item.clothing_name.trim() === '') {
    errors.push({
      row,
      field: 'clothing_name',
      value: item.clothing_name,
      message: '服装名称不能为空'
    })
  }

  // 数量验证
  if (typeof item.original_quantity !== 'number' || item.original_quantity <= 0) {
    errors.push({
      row,
      field: 'original_quantity',
      value: item.original_quantity,
      message: '初始数量必须是大于0的数字'
    })
  }

  if (typeof item.current_quantity !== 'number' || item.current_quantity < 0) {
    errors.push({
      row,
      field: 'current_quantity',
      value: item.current_quantity,
      message: '当前数量必须是大于等于0的数字'
    })
  }

  // 当前数量不能大于初始数量
  if (
    typeof item.original_quantity === 'number' &&
    typeof item.current_quantity === 'number' &&
    item.current_quantity > item.original_quantity
  ) {
    warnings.push({
      row,
      field: 'current_quantity',
      value: item.current_quantity,
      message: '当前数量大于初始数量，请确认数据正确性'
    })
  }

  // 服装ID验证
  if (!item.clothing_id && !item.oem_clothing_id) {
    warnings.push({
      row,
      field: 'clothing_id',
      value: '',
      message: '建议填写服装ID或OEM服装ID以便关联库存'
    })
  }

  if (item.clothing_id && item.oem_clothing_id) {
    warnings.push({
      row,
      field: 'clothing_id',
      value: item.clothing_id,
      message: '不建议同时填写服装ID和OEM服装ID'
    })
  }

  // 日期格式验证
  if (item.inbound_date && item.inbound_date.trim() !== '') {
    const date = new Date(item.inbound_date)
    if (isNaN(date.getTime())) {
      errors.push({
        row,
        field: 'inbound_date',
        value: item.inbound_date,
        message: '入库日期格式不正确，请使用YYYY-MM-DD格式'
      })
    } else {
      // 检查日期是否在合理范围内
      const now = new Date()
      const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
      const oneMonthLater = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate())

      if (date < oneYearAgo || date > oneMonthLater) {
        warnings.push({
          row,
          field: 'inbound_date',
          value: item.inbound_date,
          message: '入库日期超出合理范围（一年前到一个月后）'
        })
      }
    }
  }

  // 系列号验证
  if (item.series_number !== undefined && item.series_number !== null) {
    if (typeof item.series_number !== 'number' || item.series_number < 0) {
      errors.push({
        row,
        field: 'series_number',
        value: item.series_number,
        message: '系列号必须是大于等于0的数字'
      })
    }
  }
}


/**
 * 格式化验证结果为可读的错误信息
 */
export function formatValidationResult(result: ValidationResult): string {
  const messages: string[] = []

  if (result.errors.length > 0) {
    messages.push('发现以下错误：')
    result.errors.forEach((error) => {
      messages.push(`第${error.row}行 ${error.field}: ${error.message}`)
    })
  }

  if (result.warnings.length > 0) {
    messages.push('发现以下警告：')
    result.warnings.forEach((warning) => {
      messages.push(`第${warning.row}行 ${warning.field}: ${warning.message}`)
    })
  }

  return messages.join('\n')
}

/**
 * 清理和标准化包裹初始化数据
 */
export function sanitizePackageInitializationData(
  data: any[]
): PackageInitializationItem[] {
  return data.map((item) => ({
    package_code: String(item.package_code || '').trim(),
    warehouse_id: String(item.warehouse_id || '').trim(),
    sku: String(item.sku || '').trim(),
    clothing_name: String(item.clothing_name || '').trim(),
    original_quantity: Number(item.original_quantity) || 0,
    current_quantity: Number(item.current_quantity) || 0,
    location_code: item.location_code ? String(item.location_code).trim() : undefined,
    clothing_id: item.clothing_id ? String(item.clothing_id).trim() : undefined,
    oem_clothing_id: item.oem_clothing_id ? String(item.oem_clothing_id).trim() : undefined,
    supplier: item.supplier ? String(item.supplier).trim() : undefined,
    transportation_id: item.transportation_id ? String(item.transportation_id).trim() : undefined,
    series_number: item.series_number ? Number(item.series_number) : undefined,
    inbound_date: item.inbound_date ? String(item.inbound_date).trim() : undefined
  }))
}

/**
 * 生成包裹初始化统计信息（支持混合包裹）
 */
export function generatePackageInitializationStats(data: PackageInitializationItem[]) {
  // 按包裹编码分组
  const packageGroups = new Map<string, PackageInitializationItem[]>()

  data.forEach((item) => {
    const packageCode = item.package_code
    if (!packageGroups.has(packageCode)) {
      packageGroups.set(packageCode, [])
    }
    packageGroups.get(packageCode)!.push(item)
  })

  const stats = {
    totalExcelRows: data.length,
    totalPackages: packageGroups.size,
    singlePackages: 0,
    mixedPackages: 0,
    totalOriginalQuantity: 0,
    totalCurrentQuantity: 0,
    warehouseCount: new Set<string>(),
    skuCount: new Set<string>(),
    supplierCount: new Set<string>(),
    hasClothingId: 0,
    hasOemClothingId: 0,
    hasTransportationId: 0,
    hasLocationCode: 0
  }

  // 统计包裹类型和其他信息
  for (const [, items] of packageGroups) {
    if (items.length === 1) {
      stats.singlePackages++
    } else {
      stats.mixedPackages++
    }

    // 统计每个包裹的信息
    items.forEach((item) => {
      stats.totalOriginalQuantity += item.original_quantity
      stats.totalCurrentQuantity += item.current_quantity
      stats.warehouseCount.add(item.warehouse_id)
      stats.skuCount.add(item.sku)

      if (item.supplier) stats.supplierCount.add(item.supplier)
      if (item.clothing_id) stats.hasClothingId++
      if (item.oem_clothing_id) stats.hasOemClothingId++
      if (item.transportation_id) stats.hasTransportationId++
      if (item.location_code) stats.hasLocationCode++
    })
  }

  return {
    ...stats,
    warehouseCount: stats.warehouseCount.size,
    skuCount: stats.skuCount.size,
    supplierCount: stats.supplierCount.size,
    averageQuantityPerPackage: Math.round(stats.totalOriginalQuantity / stats.totalPackages),
    utilizationRate: Math.round((stats.totalCurrentQuantity / stats.totalOriginalQuantity) * 100)
  }
}
