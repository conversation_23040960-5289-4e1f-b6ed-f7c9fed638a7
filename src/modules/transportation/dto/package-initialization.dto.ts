import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNumber, IsOptional, IsDateString, IsArray, ValidateNested, Min, IsEnum } from 'class-validator'
import { Type } from 'class-transformer'

/**
 * 包裹内容项DTO - 表示包裹中的单个产品
 */
export class PackageContentItemDto {
  @ApiProperty({ description: '产品SKU' })
  @IsString()
  sku: string

  @ApiProperty({ description: '服装名称' })
  @IsString()
  clothing_name: string

  @ApiProperty({ description: '初始数量' })
  @IsNumber()
  @Min(0)
  original_quantity: number

  @ApiProperty({ description: '当前数量' })
  @IsNumber()
  @Min(0)
  current_quantity: number

  @ApiProperty({ description: '服装ID（普通服装）', required: false })
  @IsOptional()
  @IsString()
  clothing_id?: string

  @ApiProperty({ description: 'OEM服装ID', required: false })
  @IsOptional()
  @IsString()
  oem_clothing_id?: string
}

/**
 * 包裹初始化项DTO - 支持单一和混合包裹
 */
export class PackageInitializationItemDto {
  @ApiProperty({ description: '包裹编码（唯一标识）' })
  @IsString()
  package_code: string

  @ApiProperty({ description: '仓库ID' })
  @IsString()
  warehouse_id: string

  @ApiProperty({ description: '包裹类型', enum: ['single', 'mixed'] })
  @IsEnum(['single', 'mixed'])
  package_type: 'single' | 'mixed'

  @ApiProperty({ description: '包裹内容列表', type: [PackageContentItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageContentItemDto)
  contents: PackageContentItemDto[]

  @ApiProperty({ description: '仓库位置码', required: false })
  @IsOptional()
  @IsString()
  location_code?: string

  @ApiProperty({ description: '供应商', required: false })
  @IsOptional()
  @IsString()
  supplier?: string

  @ApiProperty({ description: '关联货运单ID', required: false })
  @IsOptional()
  @IsString()
  transportation_id?: string

  @ApiProperty({ description: '货运单系列号', required: false })
  @IsOptional()
  @IsNumber()
  series_number?: number

  @ApiProperty({ description: '入库日期', required: false })
  @IsOptional()
  @IsDateString()
  inbound_date?: string
}

/**
 * Excel行数据DTO - 用于接收Excel解析的原始数据
 */
export class ExcelRowDataDto {
  @ApiProperty({ description: '包裹编码（唯一标识）' })
  @IsString()
  package_code: string

  @ApiProperty({ description: '仓库ID' })
  @IsString()
  warehouse_id: string

  @ApiProperty({ description: '产品SKU' })
  @IsString()
  sku: string

  @ApiProperty({ description: '服装名称' })
  @IsString()
  clothing_name: string

  @ApiProperty({ description: '初始数量' })
  @IsNumber()
  @Min(0)
  original_quantity: number

  @ApiProperty({ description: '当前数量' })
  @IsNumber()
  @Min(0)
  current_quantity: number

  @ApiProperty({ description: '仓库位置码', required: false })
  @IsOptional()
  @IsString()
  location_code?: string

  @ApiProperty({ description: '服装ID（普通服装）', required: false })
  @IsOptional()
  @IsString()
  clothing_id?: string

  @ApiProperty({ description: 'OEM服装ID', required: false })
  @IsOptional()
  @IsString()
  oem_clothing_id?: string

  @ApiProperty({ description: '供应商', required: false })
  @IsOptional()
  @IsString()
  supplier?: string

  @ApiProperty({ description: '关联货运单ID', required: false })
  @IsOptional()
  @IsString()
  transportation_id?: string

  @ApiProperty({ description: '货运单系列号', required: false })
  @IsOptional()
  @IsNumber()
  series_number?: number

  @ApiProperty({ description: '入库日期', required: false })
  @IsOptional()
  @IsDateString()
  inbound_date?: string
}

/**
 * 包裹初始化请求DTO - 支持两种输入方式
 */
export class PackageInitializationDto {
  @ApiProperty({ description: '包裹初始化数据列表（结构化数据）', type: [PackageInitializationItemDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageInitializationItemDto)
  packages?: PackageInitializationItemDto[]

  @ApiProperty({ description: 'Excel行数据列表（原始数据）', type: [ExcelRowDataDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExcelRowDataDto)
  excel_rows?: ExcelRowDataDto[]

  @ApiProperty({ description: '操作员', required: false })
  @IsOptional()
  @IsString()
  operator?: string

  @ApiProperty({ description: '备注', required: false })
  @IsOptional()
  @IsString()
  notes?: string
}

/**
 * 包裹初始化响应DTO
 */
export class PackageInitializationResponseDto {
  @ApiProperty({ description: '响应代码' })
  code: number

  @ApiProperty({ description: '响应消息' })
  message: string

  @ApiProperty({ description: '响应数据' })
  data: {
    total_requested: number
    successful_created: number
    failed_created: number
    batch_code: string
    created_packages: string[]
    failed_packages: Array<{
      package_code: string
      reason: string
    }>
  }
}
