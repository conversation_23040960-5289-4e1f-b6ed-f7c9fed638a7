import http from "../http.js"; // 引入封装好的请求方法

// ==================== 出库管理相关 API ====================

// 创建出库单
export const createOutboundOrder = (params) => {
  return http({
    url: "/miniprogram/createOutboundOrder",
    method: "post",
    data: params,
  });
};

// 获取出库单列表
export const getOutboundOrderList = (params) => {
  return http({
    url: "/miniprogram/outboundOrderList",
    method: "get",
    data: params,
  });
};

// 获取出库单详情
export const getOutboundOrderDetail = (params) => {
  return http({
    url: "/miniprogram/outboundOrderDetail",
    method: "get",
    data: params,
  });
};
