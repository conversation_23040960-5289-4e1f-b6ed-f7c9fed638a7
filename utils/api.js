// 统一导入所有分类的 API
import * as authApi from './api/auth.js';
import * as transportationApi from './api/transportation.js';
import * as clothingApi from './api/clothing.js';
import * as warehouseApi from './api/warehouse.js';
import * as inboundApi from './api/inbound.js';
import * as outboundApi from './api/outbound.js';


// 统一导出所有 API，保持原有的导出结构
export default {
  // ==================== 认证相关 API ====================
  login: authApi.login,
  userLogin: authApi.userLogin,
  addNewUser: authApi.addNewUser,
  getOpenId: authApi.getOpenId,
  register: authApi.register,

  // ==================== 货运相关 API ====================
  getTransportationList: transportationApi.getTransportationList,
  getTransportationSupplierOptions: transportationApi.getTransportationSupplierOptions,
  updateArrivedDate: transportationApi.updateArrivedDate,
  getTransportationDetail: transportationApi.getTransportationDetail,
  getInboundTransportationDetail: transportationApi.getInboundTransportationDetail,
  getPendingInboundTransportations: transportationApi.getPendingInboundTransportations,

  // ==================== 服装相关 API ====================
  getClothingInfo: clothingApi.getClothingInfo,
  getOemClothingInfo: clothingApi.getOemClothingInfo,
  searchClothing: clothingApi.searchClothing,
  searchOemClothing: clothingApi.searchOemClothing,
  changePrice: clothingApi.changePrice,
  searchShippedClothing: clothingApi.searchShippedClothing,
  getClothingDetail: clothingApi.getClothingDetail,
  getOemClothingDetail: clothingApi.getOemClothingDetail,
  getClothingInventory: clothingApi.getClothingInventory,
  getClothingLogs: clothingApi.getClothingLogs,
  getClothingYearOptions: clothingApi.getClothingYearOptions,
  getClothingFourOptions: clothingApi.getClothingFourOptions,
  getClothingSupplierOptions: clothingApi.getClothingSupplierOptions,
  getClothingClassificationOptions: clothingApi.getClothingClassificationOptions,
  getOemClothingYearOptions: clothingApi.getOemClothingYearOptions,
  getOemSupplierOptions: clothingApi.getOemSupplierOptions,
  getOemClassificationOptions: clothingApi.getOemClassificationOptions,
  getOemTwoOptions: clothingApi.getOemTwoOptions,

  // 综合搜索相关 API
  getClothingYears: clothingApi.getClothingYears,
  getComprehensiveSearchOptions: clothingApi.getComprehensiveSearchOptions,
  getSupplierOptions: clothingApi.getSupplierOptions,
  getClassificationOptions: clothingApi.getClassificationOptions,
  getStyleOptions: clothingApi.getStyleOptions,
  getSummaryResults: clothingApi.getSummaryResults,

  // ==================== 仓库管理相关 API ====================
  getWarehouseList: warehouseApi.getWarehouseList,
  getWarehouseDetail: warehouseApi.getWarehouseDetail,
  createWarehouse: warehouseApi.createWarehouse,
  updateWarehouse: warehouseApi.updateWarehouse,
  deleteWarehouse: warehouseApi.deleteWarehouse,
  confirmOutbound: warehouseApi.confirmOutbound,

  // ==================== 新仓库管理系统 API ====================
  warehouseBatchOutbound: warehouseApi.warehouseBatchOutbound,
  warehouseTransfer: warehouseApi.warehouseTransfer,
  warehouseInventoryCheck: warehouseApi.warehouseInventoryCheck,
  getNewWarehouseInventory: warehouseApi.getNewWarehouseInventory,
  getGlobalInventorySummary: warehouseApi.getGlobalInventorySummary,
  getWarehouseOperationLogs: warehouseApi.getWarehouseOperationLogs,
  getWarehouseOperationLogsDetail: warehouseApi.getWarehouseOperationLogsDetail,
  getWarehouseOperationLogsSummary: warehouseApi.getWarehouseOperationLogsSummary,
  getInboundStatistics: warehouseApi.getInboundStatistics,
  searchWarehouseInventory: warehouseApi.searchWarehouseInventory,
  getWarehouseSummary: warehouseApi.getWarehouseSummary,
  getWarehousesWithStats: warehouseApi.getWarehousesWithStats,
  reverseOperationLog: warehouseApi.reverseOperationLog,
  reverseTransferOperation: warehouseApi.reverseTransferOperation,

  // ==================== 日历相关 API ====================
  getDailyOutboundSummary: warehouseApi.getDailyOutboundSummary,

  // ==================== 入库管理相关 API ====================
  createInboundOrder: inboundApi.createInboundOrder,
  getInboundOrderList: inboundApi.getInboundOrderList,
  getInboundOrderDetail: inboundApi.getInboundOrderDetail,
  getPackageContents: inboundApi.getPackageContents,

  // ==================== 出库管理相关 API ====================
  createOutboundOrder: outboundApi.createOutboundOrder,
  getOutboundOrderList: outboundApi.getOutboundOrderList,
  getOutboundOrderDetail: outboundApi.getOutboundOrderDetail,


};


