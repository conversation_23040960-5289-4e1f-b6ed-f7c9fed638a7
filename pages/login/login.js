// pages/login/login.js
import Api from "../../utils/api.js"; // 引入封装好的API文件

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userName: "",
    userPwd: "",
    isLogin: false,
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    // 检查是否已有token
    const token = wx.getStorageSync("tokenKey");
    if (token) {
      // 已登录，跳转到主页
      this.setData({ isLogin: true });
      setTimeout(() => {
        wx.switchTab({
          url: "/pages/transportations/transportations",
        });
      }, 1000);
    } else {
      // 未登录，显示登录界面
      this.setData({ isLogin: false });
    }
  },

  /**
   * 获取微信OpenID
   */
  async getOpenid() {
    try {
      const { code } = await wx.login();
      const params = {
        code: code,
      };
      return await Api.getOpenId(params);
    } catch (error) {
      console.error("获取微信授权码失败:", error);
      throw error;
    }
  },

  /**
   * 表单验证
   */
  validateLoginForm() {
    // 用户名验证
    if (!this.data.userName || this.data.userName.trim() === "") {
      wx.showToast({
        title: "请输入用户名",
        icon: "none",
      });
      return false;
    }

    // 密码验证
    if (!this.data.userPwd || this.data.userPwd.trim() === "") {
      wx.showToast({
        title: "请输入密码",
        icon: "none",
      });
      return false;
    }

    if (this.data.userPwd.length < 6) {
      wx.showToast({
        title: "密码长度不能少于6个字符",
        icon: "none",
      });
      return false;
    }

    return true;
  },

  /**
   * 登录处理
   */
  async onLogin() {
    if (this.data.loading) return;

    // 表单验证
    if (!this.validateLoginForm()) {
      return;
    }

    this.setData({ loading: true });

    try {
      // 使用用户名密码登录
      const loginParams = {
        userName: this.data.userName.trim(),
        userPwd: this.data.userPwd,
      };

      const loginRes = await Api.userLogin(loginParams);

      // 登录成功，后端返回token和用户信息
      if (loginRes && loginRes.data && loginRes.data.token) {
        // 保存token
        wx.setStorageSync("tokenKey", loginRes.data.token);

        wx.showToast({
          title: "登录成功",
          icon: "success",
        });

        // 跳转到主页
        setTimeout(() => {
          wx.switchTab({
            url: "/pages/transportations/transportations",
          });
        }, 1000);
      } else {
        wx.showToast({
          title: "登录失败，请检查用户名和密码",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("登录失败:", error);

      // 处理后端返回的错误信息
      let errorMessage = "登录失败，请重试";
      if (error.data && error.data.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      wx.showToast({
        title: errorMessage,
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 输入用户名
   */
  onUserNameInput(e) {
    this.setData({
      userName: e.detail.value.trim(),
    });
  },

  /**
   * 输入用户名（旧方法，保持兼容）
   */
  inputUserName(e) {
    this.setData({
      userName: e.detail.value,
    });
  },

  /**
   * 输入密码
   */
  inputPassword(e) {
    this.setData({
      userPwd: e.detail.value,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 跳转到注册页面
   */
  goToRegister() {
    wx.navigateTo({
      url: "/pages/register/register",
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
