// pages/outbound/outbound.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 仓库列表
    warehouseList: [],
    // 当前选中的仓库
    selectedWarehouse: null,
    // 当前仓库的库存列表（新API数据结构）
    inventoryList: [],
    // 出库清单（新的数据结构）
    outboundList: [],
    // 删除了usedPackageCodes，不再需要跟踪包裹代码
    // 按仓库分组的出库清单显示数据
    groupedCart: [],
    // 购物车统计
    cartTotalPackages: 0,
    cartTotalPieces: 0,
    // 显示购物车浮窗
    showCart: false,
    // 显示服装信息弹窗
    showClothingInfo: false,
    // 当前选择的服装信息
    selectedClothingInfo: null,
    // 加载状态
    loading: false,
    // 统计数据
    totalPackages: 0,
    totalPieces: 0,
    // 搜索相关
    searchKeyword: "",
    isSearchMode: false,
    searchResults: [],
    searchLoading: false,
    originalWarehouseList: [], // 保存原始仓库列表
  },

  // 定时器引用，用于清理
  _timers: [],

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 确保 _timers 数组被正确初始化
    if (!this._timers) {
      this._timers = [];
    }
    this.loadWarehouses();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 清理所有定时器
    if (this._timers && this._timers.length > 0) {
      this._timers.forEach((timer) => {
        if (timer) {
          clearTimeout(timer);
        }
      });
      this._timers = [];
    }
    console.log("页面卸载，已清理定时器");
  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    try {
      // 检查是否需要清空待出库清单（通过全局变量标记）
      const app = getApp();
      if (app.globalData && app.globalData.shouldClearOutboundList) {
        console.log("出库成功后返回，清空待出库清单");
        this.clearOutboundListAfterConfirm();
        app.globalData.shouldClearOutboundList = false; // 重置标记

        // 清空待出库清单后，重新加载仓库数据
        await this.loadWarehouses();
      } else {
        // 页面显示时刷新仓库列表，以防从仓库详情页面返回时有新增或修改
        await this.loadWarehouses();

        // 如果有待出库清单，需要重新计算库存显示
        if (this.data.outboundList && this.data.outboundList.length > 0) {
          console.log("从核对出货页面返回，重新计算库存显示");
          this.recalculateInventoryDisplayOnReturn();
        }
      }
    } catch (error) {
      console.error("页面显示时处理失败:", error);
      // 确保loading状态被重置
      this.setData({ loading: false });
    }
  },

  /**
   * 加载仓库列表 - 修复loading状态管理
   */
  async loadWarehouses() {
    try {
      this.setData({ loading: true });
      console.log("开始加载仓库列表");

      const response = await Api.getWarehouseList({ status: "active" });

      if (response.data.code === 200) {
        const warehouses = response.data.data.list || [];
        console.log(`加载到${warehouses.length}个仓库`);

        this.setData({
          warehouseList: warehouses,
          originalWarehouseList: warehouses, // 保存原始列表
          selectedWarehouse: warehouses[0] || null,
        });

        // 加载第一个仓库的库存
        if (warehouses.length > 0) {
          await this.loadWarehouseInventory(warehouses[0].warehouse_id);
        } else {
          // 如果没有仓库，确保重置loading状态
          this.setData({ loading: false });
        }
      } else {
        console.error("加载仓库列表失败:", response.data.message);
        wx.showToast({
          title: response.data.message || "加载失败",
          icon: "none",
        });
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error("加载仓库列表异常:", error);
      wx.showToast({
        title: "加载失败",
        icon: "none",
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 加载仓库库存 
   */
  async loadWarehouseInventory(warehouseId) {
    try {
      console.log(`开始加载仓库${warehouseId}的库存`);

      if (!warehouseId) {
        this.setData({
          inventoryList: [],
          totalPackages: 0,
          totalPieces: 0,
          loading: false,
        });
        return;
      }

      const response = await Api.getNewWarehouseInventory({
        warehouse_id: warehouseId,
        limit: 1000, // 设置足够大的limit以获取所有数据
      });
      console.log("获取到的库存数据616:", response.data);

      if (response.data.code === 200) {
        const inventoryData = response.data.data.list || [];
        console.log(
          `仓库${warehouseId}库存数据加载成功，共${inventoryData.length}项`
        );

        // 新的数据结构：按classification_code分组的库存数据
        const processedInventory = this.processClassificationCodeInventory(
          inventoryData,
          warehouseId
        );
        console.log(`处理后的库存数据，共${processedInventory.length}项`);
        console.log("处理后的库存数据:", processedInventory);

        // 修复数据抖动：如果有待出库清单，在设置数据前先计算最终状态
        if (this.data.outboundList && this.data.outboundList.length > 0) {
          console.log("检测到待出库清单，预先计算库存状态避免抖动");
          // 计算最终的库存状态
          const currentWarehouseOutbounds = this.data.outboundList.filter(
            (item) => item.warehouse_id === warehouseId
          );

          if (currentWarehouseOutbounds.length > 0) {
            const finalInventoryList = this.calculateFinalInventoryStateNew(
              processedInventory,
              currentWarehouseOutbounds
            );
            this.setData({
              inventoryList: finalInventoryList,
              loading: false,
            });
          } else {
            this.setData({
              inventoryList: processedInventory,
              loading: false,
            });
          }
        } else {
          // 没有待出库清单，直接设置
          this.setData({
            inventoryList: processedInventory,
            loading: false,
          });
        }

        // 使用后端返回的汇总数据，而不是前端重新计算
        const summaryData = response.data.data.summary || {};
        console.log("后端汇总数据:", summaryData);

        this.setData({
          totalPackages: Math.round((summaryData.total_packages || 0) * 100) / 100,
          totalPieces: summaryData.total_pieces || 0,
        });

        console.log(`仓库${warehouseId}库存加载完成`);
      } else {
        console.error("加载库存失败:", response.data.message);
        wx.showToast({
          title: response.data.message || "加载库存失败",
          icon: "none",
        });
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error("加载库存异常:", error);
      wx.showToast({
        title: "加载库存失败",
        icon: "none",
      });
      this.setData({ loading: false });
    }
  },

  /**
   * 处理新的classification_code库存数据结构 - 简化版本
   */
  processClassificationCodeInventory(inventoryData, warehouseId) {
    return inventoryData.map((item, index) => {
      // 生成唯一ID（基于classification_code）
      const unique_id = `${warehouseId}_${
        item.classification_code
      }_${Date.now()}_${index}`;

      return {
        ...item,
        warehouse_id: warehouseId,
        unique_id: unique_id,
        classification_code: item.classification_code,
        // 为每个contents项添加选择数量和可用数量
        contents: item.contents.map((content) => ({
          ...content,
          // selectedQuantity: 0,
          // available_quantity: content.current_quantity,
        })),
        selectedPackageQuantity: 0,
        availablePackageCount: item.package_count,
        availableTotalQuantity: item.total_quantity,
        // 如果是多货物包裹，初始化计算包数
        calculatedPackageQuantity:
          item.contents.length > 1 ? "0.00" : undefined,
      };
    });
  },

  /**
   * 计算库存统计
   */
  calculateInventoryStats(inventoryData) {
    let totalPackages = 0;
    let totalPieces = 0;

    inventoryData.forEach((item) => {
      totalPackages += item.package_count || 0;
      totalPieces += item.total_quantity || 0;
    });

    this.setData({
      totalPackages: Math.round(totalPackages * 100) / 100,
      totalPieces,
    });
  },

  /**
   * 计算最终库存状态 - 根据package_type简化逻辑
   */
  calculateFinalInventoryStateNew(inventoryList, outboundList) {
    return inventoryList.map((inventoryItem) => {
      // 按classification_code匹配出库记录
      const relatedOutbounds = outboundList.filter((item) => {
        return (
          item.classification_code === inventoryItem.classification_code &&
          item.warehouse_id === inventoryItem.warehouse_id
        );
      });

      if (relatedOutbounds.length > 0) {
        // 根据package_type区分处理逻辑
        if (inventoryItem.package_type === 'single') {
          // 单一包裹处理逻辑
          const totalOutboundPackages = relatedOutbounds.reduce(
            (sum, item) => sum + (parseFloat(item.package_count) || 0),
            0
          );

          // 计算动态包裹数
          const availablePackageCount = Math.max(
            0,
            inventoryItem.package_count - totalOutboundPackages
          );

          // 计算动态总件数
          const originalQuantityPerPackage = inventoryItem.contents[0].original_quantity;
          const availableTotalQuantity = Math.max(
            0,
            inventoryItem.total_quantity - (originalQuantityPerPackage * totalOutboundPackages)
          );

          // 更新contents的可用数量
          const updatedContents = inventoryItem.contents.map((content) => ({
            ...content,
            available_quantity: availableTotalQuantity,
            selectedQuantity: 0,
          }));

          return {
            ...inventoryItem,
            contents: updatedContents,
            availablePackageCount,
            availableTotalQuantity,
            selectedPackageQuantity: 0,
          };

        } else if (inventoryItem.package_type === 'mixed') {
          // 混合包裹处理逻辑 - 不需要动态总件数、动态总包裹数
          const updatedContents = inventoryItem.contents.map((content) => {
            // 计算该content的总出库数量
            const totalOutboundForThisContent = relatedOutbounds.reduce(
              (sum, item) => {
                const matchingContent = item.contents.find(
                  (c) => c.sku === content.sku
                );
                return (
                  sum +
                  (matchingContent
                    ? matchingContent.outbound_quantity ||
                      matchingContent.selectedQuantity ||
                      0
                    : 0)
                );
              },
              0
            );

            // 动态可出库件数 = current_quantity - 出库件数
            return {
              ...content,
              available_quantity: Math.max(
                0,
                content.current_quantity - totalOutboundForThisContent
              ),
              selectedQuantity: 0,
            };
          });

          return {
            ...inventoryItem,
            contents: updatedContents,
            selectedPackageQuantity: 0,
            calculatedPackageQuantity: "0.00",
          };
        }
      } else {
        // 没有出库记录的库存项，恢复到原始状态
        if (inventoryItem.package_type === 'single') {
          return {
            ...inventoryItem,
            availablePackageCount: inventoryItem.package_count,
            availableTotalQuantity: inventoryItem.total_quantity,
            selectedPackageQuantity: 0,
            contents: inventoryItem.contents.map((content) => ({
              ...content,
              available_quantity: content.current_quantity,
              selectedQuantity: 0,
            })),
          };
        } else if (inventoryItem.package_type === 'mixed') {
          return {
            ...inventoryItem,
            selectedPackageQuantity: 0,
            calculatedPackageQuantity: "0.00",
            contents: inventoryItem.contents.map((content) => ({
              ...content,
              available_quantity: content.current_quantity,
              selectedQuantity: 0,
            })),
          };
        }
      }

      // 默认返回原始状态
      return inventoryItem;
    });
  },


  /**
   * 选择仓库 - 确保仓库切换时库存计算正确，支持搜索模式
   */
  onSelectWarehouse(e) {
    const { warehouse } = e.currentTarget.dataset;
    const previousWarehouseId = this.data.selectedWarehouse?.warehouse_id;

    this.setData({
      selectedWarehouse: warehouse,
      showCart: false,
    });

    if (this.data.isSearchMode) {
      // 搜索模式下，从搜索结果中找到对应仓库的库存
      const searchGroup = this.data.searchResults.find(
        (group) => group.warehouse.warehouse_id === warehouse.warehouse_id
      );

      if (searchGroup) {
        // 修复搜索模式下切换仓库的库存计算问题
        let finalInventoryList = searchGroup.inventory;
        if (this.data.outboundList && this.data.outboundList.length > 0) {
          console.log("搜索模式下切换仓库，检测到待出库清单，计算最终库存状态");
          const currentWarehouseOutbounds = this.data.outboundList.filter(
            (item) => item.warehouse_id === warehouse.warehouse_id
          );

          if (currentWarehouseOutbounds.length > 0) {
            finalInventoryList = this.calculateFinalInventoryStateNew(
              finalInventoryList,
              currentWarehouseOutbounds
            );
          }
        }

        this.setData({
          inventoryList: finalInventoryList,
        });

        // 计算统计数据 - 使用原始库存数据
        const originalInventoryData = searchGroup.inventory.map((item) => ({
          package_count: item.package_count || 0,
          total_quantity: item.total_quantity || 0,
        }));
        this.calculateInventoryStats(originalInventoryData);
      }
    } else {
      // 正常模式下加载仓库库存
      this.loadWarehouseInventory(warehouse.warehouse_id);
    }

    // 如果切换了仓库且有待出库清单，需要重新计算新仓库的库存显示
    if (
      previousWarehouseId !== warehouse.warehouse_id &&
      this.data.outboundList &&
      this.data.outboundList.length > 0
    ) {
      console.log(
        `仓库切换：${previousWarehouseId} -> ${warehouse.warehouse_id}，重新计算库存显示`
      );
      // 延迟执行，确保新仓库的库存数据已加载完成
      const timer = setTimeout(() => {
        this.recalculateCurrentWarehouseInventory(warehouse.warehouse_id);
      }, 500);

      // 确保 _timers 数组存在
      if (!this._timers) {
        this._timers = [];
      }
      this._timers.push(timer);
    }
  },

  /**
   * 单货物包裹数量变化（步进器）- 步进器为0.5
   */
  onSingleItemQuantityChange(e) {
    const { inventoryIndex } = e.currentTarget.dataset;
    const quantity = parseFloat(e.detail) || 0;

    const inventoryList = this.data.inventoryList;
    const updatedInventory = inventoryList.map((item, index) => {
      if (index === inventoryIndex) {
        return {
          ...item,
          selectedPackageQuantity: quantity,
        };
      }
      return item;
    });

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 多货物包裹中单个货物数量变化
   */
  onMultiItemQuantityChange(e) {
    const { inventoryIndex, contentIndex } = e.currentTarget.dataset;
    const quantity = Math.max(0, parseInt(e.detail) || 0);

    const inventoryList = this.data.inventoryList;
    const updatedInventory = inventoryList.map((item, index) => {
      if (index === inventoryIndex) {
        const updatedContents = item.contents.map((content, cIndex) => {
          if (cIndex === contentIndex) {
            // 限制输入数量不超过可用库存
            const availableQuantity =
              content.available_quantity !== undefined
                ? content.available_quantity
                : content.current_quantity;
            const limitedQuantity = Math.min(quantity, availableQuantity);
            return {
              ...content,
              selectedQuantity: limitedQuantity,
            };
          }
          return content;
        });

        // 计算出货包数
        const selectedTotalPieces = updatedContents.reduce(
          (sum, content) => sum + (content.selectedQuantity || 0),
          0
        );
        // 使用original_quantity而不是current_quantity，确保库存为0的货物也被计入包装规格
        const originalTotalPieces = updatedContents.reduce(
          (sum, content) => sum + content.original_quantity,
          0
        );
        const calculatedPackageQuantity =
          originalTotalPieces > 0
            ? Math.round((selectedTotalPieces / originalTotalPieces) * 100) /
              100
            : "0.00";

        return {
          ...item,
          contents: updatedContents,
          calculatedPackageQuantity,
        };
      }
      return item;
    });

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 单货物包裹出库按钮 - 修复键盘弹出时的同步问题
   */
  async onSingleItemOutbound(e) {
    const { inventoryIndex } = e.currentTarget.dataset;
    const item = this.data.inventoryList[inventoryIndex];

    if (!this.data.selectedWarehouse) {
      wx.showToast({
        title: "请先选择仓库",
        icon: "none",
      });
      return;
    }

    if (!item.selectedPackageQuantity || item.selectedPackageQuantity <= 0) {
      wx.showToast({
        title: "请选择出库数量",
        icon: "none",
      });
      return;
    }

    // 确保键盘收起，延迟执行出库操作
    wx.hideKeyboard();

    // 延迟100ms确保键盘收起和数据同步
    setTimeout(async () => {
      await this.addSingleItemToOutboundList(item, inventoryIndex);
      // 出库后只重置当前卡片的步进器为0
      this.resetSingleItemSelection(inventoryIndex);
    }, 100);
  },

  /**
   * 多货物包裹出库按钮 - 修复键盘弹出时的同步问题
   */
  async onMultiItemOutbound(e) {
    const { inventoryIndex } = e.currentTarget.dataset;
    const item = this.data.inventoryList[inventoryIndex];

    if (!this.data.selectedWarehouse) {
      wx.showToast({
        title: "请先选择仓库",
        icon: "none",
      });
      return;
    }

    // 检查是否有选择的货物
    const selectedContents = item.contents.filter(
      (content) => content.selectedQuantity > 0
    );
    if (selectedContents.length === 0) {
      wx.showToast({
        title: "请选择要出库的货物",
        icon: "none",
      });
      return;
    }

    // 确保键盘收起，延迟执行出库操作
    wx.hideKeyboard();

    // 延迟100ms确保键盘收起和数据同步
    setTimeout(async () => {
      await this.addMultiItemToOutboundList(
        item,
        selectedContents
      );
      // 出库后只重置当前卡片的数字输入框为0
      this.resetMultiItemSelection(inventoryIndex);
    }, 100);
  },

  /**
   * 添加单货物包裹到出库清单 - 简化版本
   */
  async addSingleItemToOutboundList(item) {
    try {
      const { selectedWarehouse } = this.data;
      const outboundQuantity = item.selectedPackageQuantity;
      const content = item.contents[0];
      console.log("添加单货物包裹到出库清单:", item);

      if (outboundQuantity <= 0) {
        wx.showToast({
          title: "请选择出库数量",
          icon: "none",
        });
        return;
      }

      if (outboundQuantity > item.availablePackageCount) {
        wx.showToast({
          title: "出库数量超过可用库存",
          icon: "none",
        });
        return;
      }

      // 简化：直接计算总出库件数
      const totalOutboundPieces = Math.round(
        outboundQuantity * content.original_quantity
      );
      
      // 添加到出库清单 - 简化的classification_code格式
      this.addToOutboundList({
        warehouse_id: selectedWarehouse.warehouse_id,
        warehouse_name: selectedWarehouse.name,
        classification_code: item.classification_code,
        package_type: item.package_type,
        package_count: outboundQuantity,
        total_pieces: totalOutboundPieces,
        // 保留用于显示的基本信息，确保有outbound_quantity
        contents: [{
          ...content,
          outbound_quantity: totalOutboundPieces,
        }],
      });

    } catch (error) {
      console.error("添加单货物包裹到出库清单失败:", error);
      wx.showToast({
        title: "添加到出库清单失败",
        icon: "none",
      });
    }
  },

  /**
   * 添加多货物包裹到出库清单 - 简化版本
   */
  async addMultiItemToOutboundList(item, selectedContents) {
    try {
      const { selectedWarehouse } = this.data;

      console.log("多货物包裹出库参数:", { item, selectedContents });

      // 验证 selectedContents 是数组
      if (!Array.isArray(selectedContents)) {
        console.error("selectedContents 不是数组:", selectedContents);
        wx.showToast({
          title: "数据格式错误",
          icon: "none",
        });
        return;
      }

      // 计算出货总件数（四舍五入避免半件衣服）
      const outboundTotalPieces = Math.round(
        selectedContents.reduce(
          (sum, content) => sum + content.selectedQuantity,
          0
        )
      );

      // 计算正确的包数：当前混合包裹各个货物总的出库数除以混合包裹内各个货物original_quantity之和，保留2位小数
      const totalOriginalQuantity = item.contents.reduce(
        (sum, content) =>
          sum + (content.original_quantity || content.current_quantity),
        0
      );
      const calculatedPackageQuantity =
        totalOriginalQuantity > 0
          ? Math.round((outboundTotalPieces / totalOriginalQuantity) * 100) /
            100
          : 0;

      // 简化：不再需要包裹代码验证和复杂的状态跟踪

      // 添加到出库清单 - 简化的classification_code格式
      this.addToOutboundList({
        warehouse_id: selectedWarehouse.warehouse_id,
        warehouse_name: selectedWarehouse.name,
        classification_code: item.classification_code,
        package_type: item.package_type,
        package_count: calculatedPackageQuantity,
        total_pieces: outboundTotalPieces,
        // 保留用于显示的基本信息
        contents: selectedContents.map((content) => ({
          ...content,
          outbound_quantity: content.selectedQuantity,
        })),
      });

      // 简化：不再需要维护已使用的包裹代码数组
    } catch (error) {
      console.error("添加多货物包裹到出库清单失败:", error);
      wx.showToast({
        title: "添加到出库清单失败",
        icon: "none",
      });
    }
  },

  /**
   * 添加到出库清单 - 简化的classification_code逻辑
   */
  addToOutboundList(outboundItem) {
    const { outboundList = [] } = this.data;

    // 生成唯一ID
    const itemId = `${outboundItem.warehouse_id}_${
      outboundItem.classification_code
    }_${Date.now()}`;

    const newItem = {
      ...outboundItem,
      id: itemId,
      created_at: new Date().toISOString(),
    };

    const updatedList = [...outboundList, newItem];

    // 简化的库存显示更新
    this.updateInventoryAfterOutboundSimple(outboundItem);

    // 简化的出库清单显示更新
    this.updateOutboundListDisplaySimple(updatedList);

    this.setData({
      outboundList: updatedList,
    });

    wx.showToast({
      title: "已添加到出库清单",
      icon: "success",
      duration: 500,
    });
  },

  /**
   * 简化的库存显示更新 - 根据package_type区分处理逻辑
   */
  updateInventoryAfterOutboundSimple(outboundItem) {
    const updatedInventory = this.data.inventoryList.map((inventoryItem) => {
      // 按classification_code匹配
      if (
        inventoryItem.classification_code ===
          outboundItem.classification_code &&
        inventoryItem.warehouse_id === outboundItem.warehouse_id
      ) {
        if (inventoryItem.package_type === 'single') {
          // 单一包裹处理逻辑
          const outboundPackageCount = outboundItem.package_count;
          const originalQuantityPerPackage = inventoryItem.contents[0].original_quantity;

          // 更新动态包裹数
          const newAvailablePackageCount = Math.max(
            0,
            inventoryItem.availablePackageCount - outboundPackageCount
          );

          // 更新动态总件数
          const newAvailableTotalQuantity = Math.max(
            0,
            inventoryItem.availableTotalQuantity - (originalQuantityPerPackage * outboundPackageCount)
          );

          // 更新contents的可用数量（单一包裹中所有content的可用数量都等于总件数）
          const updatedContents = inventoryItem.contents.map((content) => ({
            ...content,
            available_quantity: newAvailableTotalQuantity,
            selectedQuantity: 0,
          }));

          return {
            ...inventoryItem,
            contents: updatedContents,
            availablePackageCount: newAvailablePackageCount,
            availableTotalQuantity: newAvailableTotalQuantity,
            selectedPackageQuantity: 0,
          };
        } else if (inventoryItem.package_type === 'mixed') {
          // 混合包裹处理逻辑 - 每种服装的可出库数量独立计算
          const updatedContents = inventoryItem.contents.map((content) => {
            // 查找出库清单中对应的content
            const outboundContent = outboundItem.contents.find(
              (outContent) => outContent.sku === content.sku
            );

            if (outboundContent) {
              // 动态可出库件数 = current_quantity - 出库件数
              const newAvailableQuantity = Math.max(
                0,
                (content.available_quantity || content.current_quantity) -
                (outboundContent.outbound_quantity || outboundContent.selectedQuantity || 0)
              );

              return {
                ...content,
                available_quantity: newAvailableQuantity,
                selectedQuantity: 0,
              };
            } else {
              // 没有出库的content，只重置选择状态
              return {
                ...content,
                selectedQuantity: 0,
              };
            }
          });

          return {
            ...inventoryItem,
            contents: updatedContents,
            selectedPackageQuantity: 0,
            calculatedPackageQuantity: "0.00",
          };
        }
      }
      return inventoryItem;
    });

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 简化的出库清单显示更新 - 根据package_type区分显示逻辑
   */
  updateOutboundListDisplaySimple(outboundList) {
    // 按仓库和classification_code分组
    const grouped = {};

    outboundList.forEach((item) => {
      const warehouseId = item.warehouse_id;
      if (!grouped[warehouseId]) {
        grouped[warehouseId] = {
          warehouse_id: warehouseId,
          warehouse_name: item.warehouse_name,
          items: [],
          total_packages: 0,
          total_pieces: 0,
        };
      }

      // 按classification_code合并
      const existingItem = grouped[warehouseId].items.find(
        (displayItem) =>
          displayItem.classification_code === item.classification_code
      );

      if (existingItem) {
        // 合并相同classification_code的项目
        existingItem.package_count += item.package_count;
        existingItem.total_pieces += item.total_pieces;

        if (item.package_type === 'single') {
          // 单一包裹：更新显示数量
          const originalQuantityPerPackage = item.contents[0].original_quantity;
          existingItem.display_quantity = originalQuantityPerPackage + '件/包 × ' + existingItem.package_count + '包 = ' + (originalQuantityPerPackage * existingItem.package_count) + '件';
        } else if (item.package_type === 'mixed') {
          // 混合包裹：合并contents的出库数量
          item.contents.forEach((newContent) => {
            const existingContent = existingItem.contents.find(
              (content) => content.sku === newContent.sku
            );

            if (existingContent) {
              // 累加出库数量
              existingContent.outbound_quantity =
                (existingContent.outbound_quantity || 0) +
                (newContent.outbound_quantity || newContent.selectedQuantity || 0);
            } else {
              // 新增content
              existingItem.contents.push({
                ...newContent,
                outbound_quantity: newContent.outbound_quantity || newContent.selectedQuantity || 0,
              });
            }
          });

          // 更新混合包裹的显示数量
          existingItem.display_quantity = existingItem.contents.map(content =>
            content.clothing_name + ': ' + (content.outbound_quantity || 0) + '件'
          ).join(', ');

          // 重新计算混合包裹的出库包数 = 所有服装出库件数之和 / 所有服装original_quantity之和
          const totalOriginalQuantity = existingItem.contents.reduce(
            (sum, content) => sum + (content.original_quantity || 0),
            0
          );
          const totalOutboundQuantity = existingItem.contents.reduce(
            (sum, content) => sum + (content.outbound_quantity || 0),
            0
          );
          existingItem.package_count = totalOriginalQuantity > 0
            ? Math.round((totalOutboundQuantity / totalOriginalQuantity) * 100) / 100
            : 0;
        }

        // 确保合并项目有display_id
        if (!existingItem.display_id) {
          existingItem.display_id = `display_${warehouseId}_${item.classification_code}_${Date.now()}`;
        }
      } else {
        // 新增项目
        const newItem = {
          classification_code: item.classification_code,
          package_type: item.package_type,
          package_count: item.package_count,
          total_pieces: item.total_pieces,
          // 添加display_id用于删除操作
          display_id: `display_${warehouseId}_${item.classification_code}_${Date.now()}`,
          // 保存原始ID用于删除时查找
          id: item.id,
          // 确保contents有正确的outbound_quantity
          contents: item.contents.map((content) => ({
            ...content,
            outbound_quantity: content.outbound_quantity || content.selectedQuantity || 0,
          })),
        };

        if (item.package_type === 'single') {
          // 单一包裹：生成显示数量字符串
          const originalQuantityPerPackage = item.contents[0].original_quantity;
          newItem.display_quantity = originalQuantityPerPackage + '件/包 × ' + item.package_count + '包 = ' + (originalQuantityPerPackage * item.package_count) + '件';
        } else if (item.package_type === 'mixed') {
          // 混合包裹：显示各服装的出库件数
          newItem.display_quantity = item.contents.map(content =>
            content.clothing_name + ': ' + (content.outbound_quantity || content.selectedQuantity || 0) + '件'
          ).join(', ');
        }

        grouped[warehouseId].items.push(newItem);
      }

      grouped[warehouseId].total_packages += item.package_count;
      grouped[warehouseId].total_pieces += item.total_pieces;
    });

    const groupedCart = Object.values(grouped);
    const cartTotalPackages = outboundList.reduce(
      (sum, item) => sum + item.package_count,
      0
    );
    const cartTotalPieces = outboundList.reduce(
      (sum, item) => sum + item.total_pieces,
      0
    );
    console.log("显示优化更新后的出库清单:", groupedCart);
    this.setData({
      groupedCart,
      cartTotalPackages: Math.round(cartTotalPackages * 100) / 100,
      cartTotalPieces,
    });
  },

  /**
   * 点击服装名称
   */
  async onClothingNameTap(e) {
    const { content } = e.currentTarget.dataset;

    // 显示加载状态
    wx.showLoading({
      title: "加载中...",
      mask: true,
    });

    try {
      let clothingInfo;

      if (content.is_oem) {
        // 获取OEM服装详细信息
        console.log(
          "获取OEM服装信息，ID:",
          content.oem_clothing_id || content.clothing_id
        );
        const response = await Api.getOemClothingInfo({
          oem_clothing_id: content.oem_clothing_id || content.clothing_id,
        });

        console.log("OEM服装API响应:", response);

        // 检查多种可能的响应格式
        let oemData = null;
        if (response.data) {
          if (response.data.code === 200) {
            // 标准格式：{ code: 200, data: {...}, message: "..." }
            oemData = response.data.data;
          } else if (response.data.oem_clothing_id) {
            // 直接返回数据格式
            oemData = response.data;
          }
        } else if (response.oem_clothing_id) {
          // 最简单的格式，直接是数据
          oemData = response;
        }

        if (oemData && oemData.oem_clothing_id) {
          clothingInfo = {
            oem_clothing_id: oemData.oem_clothing_id,
            oem_clothing_name: oemData.oem_clothing_name,
            oem_supplier: oemData.oem_supplier || "",
            classification: oemData.classification || "",
            price: oemData.price || 0,
            img: oemData.img || [],
            is_oem: true,
            size: oemData.size || "",
            style: oemData.style || "",
            order_quantity: oemData.order_quantity || 0,
            shipments: oemData.shipments || 0,
            in_pcs: oemData.in_pcs || 0,
          };
        } else {
          console.error("OEM服装API响应格式不正确:", response);
          throw new Error("获取OEM服装信息失败");
        }
      } else {
        // 获取普通服装详细信息
        console.log("获取普通服装信息，ID:", content.clothing_id);
        const response = await Api.getClothingInfo({
          clothing_id: content.clothing_id,
        });

        console.log("普通服装API响应:", response);

        // 检查多种可能的响应格式
        let clothingData = null;
        if (response.data) {
          if (response.data.code === 200) {
            // 标准格式：{ code: 200, data: {...}, message: "..." }
            clothingData = response.data.data;
          } else if (response.data.clothing_id) {
            // 直接返回数据格式
            clothingData = response.data;
          }
        } else if (response.clothing_id) {
          // 最简单的格式，直接是数据
          clothingData = response;
        }

        if (clothingData && clothingData.clothing_id) {
          clothingInfo = {
            clothing_id: clothingData.clothing_id,
            clothing_name: clothingData.clothing_name,
            sku: clothingData.sku || "",
            supplier: clothingData.supplier || "",
            price: clothingData.price || 0,
            img: clothingData.img || [],
            is_oem: false,
            group_classification: clothingData.group_classification || [],
            size: clothingData.size || "",
            style: clothingData.style || "",
            long_or_short_sleeve: clothingData.long_or_short_sleeve || "",
            pocket_type: clothingData.pocket_type || "",
            order_quantity: clothingData.order_quantity || 0,
            clipping_pcs: clothingData.clipping_pcs || 0,
            shipments: clothingData.shipments || 0,
          };
        } else {
          console.error("普通服装API响应格式不正确:", response);
          throw new Error("获取服装信息失败");
        }
      }

      console.log("构造的服装信息:", clothingInfo);

      this.setData({
        selectedClothingInfo: clothingInfo,
        showClothingInfo: true,
      });
    } catch (error) {
      console.error("获取服装详细信息失败:", error);
      wx.showToast({
        title: "获取服装信息失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 关闭服装信息弹窗
   */
  closeClothingInfo() {
    this.setData({
      showClothingInfo: false,
      selectedClothingInfo: null,
    });
  },

  /**
   * 切换购物车显示
   */
  toggleCart() {
    this.setData({
      showCart: !this.data.showCart,
    });
  },

  /**
   * 重置选择数量 - 出库后将步进器归零
   */
  resetSelectionQuantities() {
    const updatedInventory = this.data.inventoryList.map((inventoryItem) => ({
      ...inventoryItem,
      selectedPackageQuantity: 0, // 单货物包裹步进器归零
      calculatedPackageQuantity:
        inventoryItem.contents.length > 1 ? "0.00" : undefined, // 多货物包裹计算包数归零
      contents: inventoryItem.contents.map((content) => ({
        ...content,
        selectedQuantity: 0, // 多货物包裹中每个货物的选择数量归零
      })),
    }));

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 重置单货物包裹的选择数量 - 只重置指定卡片的步进器
   */
  resetSingleItemSelection(inventoryIndex) {
    const inventoryList = this.data.inventoryList;
    const updatedInventory = inventoryList.map((item, index) => {
      if (index === inventoryIndex) {
        return {
          ...item,
          selectedPackageQuantity: 0, // 只重置当前卡片的步进器为0
        };
      }
      return item;
    });

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 重置多货物包裹的选择数量 - 只重置指定卡片的数字输入框
   */
  resetMultiItemSelection(inventoryIndex) {
    const inventoryList = this.data.inventoryList;
    const updatedInventory = inventoryList.map((item, index) => {
      if (index === inventoryIndex) {
        return {
          ...item,
          calculatedPackageQuantity: "0.00", // 重置计算包数为0
          contents: item.contents.map((content) => ({
            ...content,
            selectedQuantity: 0, // 重置所有货物的选择数量为0
          })),
        };
      }
      return item;
    });

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 出库确认后清空待出库清单并关闭弹窗
   */
  clearOutboundListAfterConfirm() {
    // 恢复所有库存显示 - 根据package_type区分处理
    const updatedInventory = this.data.inventoryList.map((inventoryItem) => {
      if (inventoryItem.package_type === 'single') {
        return {
          ...inventoryItem,
          availablePackageCount: inventoryItem.package_count,
          availableTotalQuantity: inventoryItem.total_quantity,
          selectedPackageQuantity: 0, // 步进器归零
          contents: inventoryItem.contents.map((content) => ({
            ...content,
            available_quantity: content.current_quantity,
            selectedQuantity: 0, // 选择数量归零
          })),
        };
      } else if (inventoryItem.package_type === 'mixed') {
        return {
          ...inventoryItem,
          selectedPackageQuantity: 0, // 步进器归零
          calculatedPackageQuantity: "0.00",
          contents: inventoryItem.contents.map((content) => ({
            ...content,
            available_quantity: content.current_quantity,
            selectedQuantity: 0, // 选择数量归零
          })),
        };
      } else {
        // 默认处理（兼容旧数据）
        return {
          ...inventoryItem,
          availablePackageCount: inventoryItem.package_count,
          availableTotalQuantity: inventoryItem.total_quantity,
          selectedPackageQuantity: 0,
          calculatedPackageQuantity:
            inventoryItem.contents.length > 1 ? "0.00" : undefined,
          contents: inventoryItem.contents.map((content) => ({
            ...content,
            available_quantity: content.current_quantity,
            selectedQuantity: 0,
          })),
        };
      }
    });

    this.setData({
      outboundList: [],
      groupedCart: [],
      cartTotalPackages: 0,
      cartTotalPieces: 0,
      showCart: false, // 关闭待出库清单弹窗
      inventoryList: updatedInventory,
    });

    console.log("待出库清单已清空，弹窗已关闭");
  },

  /**
   * 从核对出货页面返回时重新计算库存显示 - 修复加载卡住问题
   */
  recalculateInventoryDisplayOnReturn() {
    // 使用更可靠的方式等待库存数据加载完成
    const checkAndRecalculate = (retryCount = 0) => {
      if (this.data.inventoryList && this.data.inventoryList.length > 0) {
        console.log(
          "重新计算库存显示，当前待出库清单:",
          this.data.outboundList
        );

        try {
          // 使用简化的方法重新计算库存显示
          const finalInventoryList = this.calculateFinalInventoryStateNew(
            this.data.inventoryList,
            this.data.outboundList
          );
          this.setData({ inventoryList: finalInventoryList });

          // 更新出库清单显示
          this.updateOutboundListDisplaySimple(this.data.outboundList);

          console.log("库存显示重新计算完成");
        } catch (error) {
          console.error("重新计算库存显示失败:", error);
        }
      } else if (retryCount < 10) {
        // 如果库存数据还没加载完成，继续等待，最多重试10次
        console.log(`库存数据未加载完成，重试第${retryCount + 1}次`);
        const timer = setTimeout(
          () => checkAndRecalculate(retryCount + 1),
          200
        );

        // 确保 _timers 数组存在
        if (!this._timers) {
          this._timers = [];
        }
        this._timers.push(timer);
      } else {
        console.warn("库存数据加载超时，跳过重新计算");
      }
    };

    // 立即检查一次，如果没有数据则开始重试机制
    checkAndRecalculate();
  },

  /**
   * 从出库清单中移除 - 简化版本，按classification_code删除
   */
  removeFromCart(e) {
    const { cartKey } = e.currentTarget.dataset;

    // 找到要删除的显示项
    const displayItem = this.data.groupedCart
      .flatMap((group) => group.items)
      .find((item) => item.display_id === cartKey);

    if (!displayItem) {
      wx.showToast({
        title: "项目不存在",
        icon: "none",
      });
      return;
    }

    console.log("删除出库清单项:", displayItem);

    // 从出库清单中删除所有相同classification_code的项目
    const updatedOutboundList = this.data.outboundList.filter(
      (item) => item.classification_code !== displayItem.classification_code
    );

    console.log("删除前出库清单长度:", this.data.outboundList.length);
    console.log("删除后出库清单长度:", updatedOutboundList.length);

    // 使用简化的方法重新计算库存显示
    const finalInventoryList = this.calculateFinalInventoryStateNew(
      this.data.inventoryList,
      updatedOutboundList
    );
    this.setData({ inventoryList: finalInventoryList });

    // 更新出库清单显示
    this.updateOutboundListDisplaySimple(updatedOutboundList);

    this.setData({
      outboundList: updatedOutboundList,
    });

    wx.showToast({
      title: "已移除",
      icon: "success",
      duration: 500,
    });
  },
  /**
   * 生成完整的出库数据结构 - 按仓库分组，包含完整仓库信息
   */
  generateCompleteOutboundData(outboundList) {
    const warehouseGroups = new Map();

    outboundList.forEach((item) => {
      // 获取仓库信息
      if (!warehouseGroups.has(item.warehouse_id)) {
        warehouseGroups.set(item.warehouse_id, {
          warehouse_id: item.warehouse_id,
          warehouse_name: item.warehouse_name,
          warehouse_address: item.warehouse_address || '',
          items: [],
          total_packages: 0,
          total_pieces: 0,
        });
      }

      const warehouseGroup = warehouseGroups.get(item.warehouse_id);

      // 查找是否已有相同的classification_code
      const existingItem = warehouseGroup.items.find(
        (i) => i.classification_code === item.classification_code
      );

      if (existingItem) {
        // 累加包裹数和件数
        existingItem.package_count += item.package_count;
        existingItem.total_pieces += item.total_pieces;

        // 合并contents的出库数量
        item.contents.forEach((newContent) => {
          const existingContent = existingItem.contents.find(
            (content) => content.sku === newContent.sku
          );
          if (existingContent) {
            existingContent.outbound_quantity =
              (existingContent.outbound_quantity || 0) +
              (newContent.outbound_quantity || newContent.selectedQuantity || 0);
          } else {
            existingItem.contents.push({
              ...newContent,
              outbound_quantity: newContent.outbound_quantity || newContent.selectedQuantity || 0,
            });
          }
        });
      } else {
        // 新增项目
        warehouseGroup.items.push({
          classification_code: item.classification_code,
          package_count: item.package_count,
          total_pieces: item.total_pieces,
          package_type: item.package_type || 'single',
          clothing_name: this.getClothingNameFromItem(item),
          contents: item.contents.map((content) => ({
            ...content,
            outbound_quantity: content.outbound_quantity || content.selectedQuantity || 0,
          })),
        });
      }

      // 更新统计数据
      warehouseGroup.total_packages += item.package_count;
      warehouseGroup.total_pieces += item.total_pieces;
    });

    // 转换Map为数组格式
    const result = Array.from(warehouseGroups.values());
    return result;
  },

  /**
   * 从出库项获取服装名称
   */
  getClothingNameFromItem(item) {
    if (item.contents && item.contents.length > 0) {
      if (item.contents.length === 1) {
        return item.contents[0].name || '未知商品';
      } else {
        return item.contents.map(content => content.name || '未知商品').join(' + ');
      }
    }
    return this.getClothingNameFromClassificationCode(item.classification_code);
  },

  /**
   * 从classification_code获取服装名称
   */
  getClothingNameFromClassificationCode(classificationCode) {
    if (!classificationCode) return '未知商品';
    const parts = classificationCode.split('_');
    if (parts.length >= 2) {
      const clothingId = parts[0] + '_' + parts[1];
      return `服装 ${clothingId}`;
    }
    return '未知商品';
  },

  /**
   * 确认出库 - 跳转到核对出货页面
   */
  async confirmOutbound() {
    const { outboundList } = this.data;

    if (outboundList.length === 0) {
      wx.showToast({
        title: "请先添加出库商品",
        icon: "none",
      });
      return;
    }

    // 计算总计数据
    const totalPackages =
      Math.round(
        outboundList.reduce(
          (sum, item) => sum + (parseFloat(item.package_count) || 0),
          0
        ) * 100
      ) / 100;
    const totalPieces = outboundList.reduce(
      (sum, item) => sum + (item.total_pieces || 0),
      0
    );

    // 生成完整的出库数据结构（包含仓库信息和按package_type分组）
    const completeOutboundData = this.generateCompleteOutboundData(outboundList);

    // 跳转到核对出货页面，传递完整的数据结构
    const orderData = {
      warehouses: completeOutboundData, // 完整的仓库分组数据
      total_packages: totalPackages,
      total_pieces: totalPieces,
    };

    console.log('传递给确认页面的完整数据:', orderData);

    wx.navigateTo({
      url: `/pages/outbound-confirm/outbound-confirm?orderData=${encodeURIComponent(
        JSON.stringify(orderData)
      )}`,
    });
  },

  /**
   * 搜索输入变化 - 适配原生input
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value,
    });
  },

  /**
   * 执行搜索
   */
  async onSearch() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: "请输入服装名称",
        icon: "none",
      });
      return;
    }

    try {
      this.setData({ searchLoading: true });
      console.log("开始搜索:", keyword);

      const response = await Api.searchWarehouseInventory({
        product_name: keyword,
      });

      if (response.data.code === 200) {
        const searchData = response.data.data?.list || [];
        console.log("搜索结果:", searchData);

        // 按仓库分组处理搜索结果 - 使用新的简化逻辑
        const groupedResults =
          this.groupSearchResultsByWarehouseSimple(searchData);

        // 构建仓库列表（只包含有搜索结果的仓库）
        const searchWarehouseList = groupedResults.map((group) => ({
          ...group.warehouse,
          inventory_count: group.inventory.length,
          total_packages: group.inventory.reduce(
            (sum, item) => sum + (item.package_count || 0),
            0
          ),
          total_pieces: group.inventory.reduce(
            (sum, item) => sum + (item.total_quantity || 0),
            0
          ),
        }));

        this.setData({
          isSearchMode: true,
          searchResults: groupedResults,
          warehouseList: searchWarehouseList,
          selectedWarehouse: searchWarehouseList[0] || null,
        });

        // 显示第一个仓库的搜索结果
        if (searchWarehouseList.length > 0) {
          const firstWarehouseResult = groupedResults[0];
          let finalInventoryList = firstWarehouseResult.inventory;

          // 如果有待出库清单，计算最终库存状态
          if (this.data.outboundList && this.data.outboundList.length > 0) {
            const currentWarehouseOutbounds = this.data.outboundList.filter(
              (item) =>
                item.warehouse_id === searchWarehouseList[0].warehouse_id
            );

            if (currentWarehouseOutbounds.length > 0) {
              finalInventoryList = this.calculateFinalInventoryStateNew(
                finalInventoryList,
                currentWarehouseOutbounds
              );
            }
          }

          this.setData({
            inventoryList: finalInventoryList,
          });

          // 计算统计数据
          const originalInventoryData = firstWarehouseResult.inventory.map(
            (item) => ({
              package_count: item.package_count || 0,
              total_quantity: item.total_quantity || 0,
            })
          );
          this.calculateInventoryStats(originalInventoryData);
        } else {
          this.setData({
            inventoryList: [],
            totalPackages: 0,
            totalPieces: 0,
          });
        }

        wx.showToast({
          title: `找到${searchWarehouseList.length}个仓库的库存`,
          icon: "success",
        });
      } else {
        wx.showToast({
          title: response.data.message || "搜索失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("搜索失败:", error);
      wx.showToast({
        title: "搜索失败",
        icon: "none",
      });
    } finally {
      this.setData({ searchLoading: false });
    }
  },

  /**
   * 按仓库分组搜索结果 - 简化版本，使用classification_code
   */
  groupSearchResultsByWarehouseSimple(searchData) {
    const warehouseMap = new Map();

    searchData.forEach((item) => {
      const warehouseId = item.warehouse_id;
      if (!warehouseMap.has(warehouseId)) {
        // 从原始仓库列表中查找仓库信息
        const originalWarehouse = this.data.originalWarehouseList.find(
          (w) => w.warehouse_id === warehouseId
        );

        warehouseMap.set(warehouseId, {
          warehouse: {
            warehouse_id: warehouseId,
            name:
              originalWarehouse?.name ||
              item.warehouse_name ||
              `仓库${warehouseId}`,
            address:
              originalWarehouse?.address ||
              item.warehouse_address ||
              "未知地址",
          },
          inventory: [],
        });
      }

      // 直接使用新的数据处理逻辑
      warehouseMap.get(warehouseId).inventory.push(item);
    });

    // 对每个仓库的数据使用新的处理方法
    for (const [warehouseId, warehouseData] of warehouseMap) {
      warehouseData.inventory = this.processClassificationCodeInventory(
        warehouseData.inventory,
        warehouseId
      );
    }

    return Array.from(warehouseMap.values());
  },

  /**
   * 重置搜索 - 修复待出库清单库存计算问题
   */
  async onResetSearch() {
    this.setData({
      searchKeyword: "",
      isSearchMode: false,
      searchResults: [],
      warehouseList: this.data.originalWarehouseList,
      selectedWarehouse: this.data.originalWarehouseList[0] || null,
    });

    // 重新加载第一个仓库的库存
    if (this.data.originalWarehouseList.length > 0) {
      await this.loadWarehouseInventory(
        this.data.originalWarehouseList[0].warehouse_id
      );

      // 如果有待出库清单，重新计算库存显示
      if (this.data.outboundList && this.data.outboundList.length > 0) {
        console.log("重置搜索后重新计算库存显示");
        this.recalculateCurrentWarehouseInventory(
          this.data.originalWarehouseList[0].warehouse_id
        );
      }
    }
  },

  /**
   * 重新计算当前仓库的库存显示
   */
  recalculateCurrentWarehouseInventory(warehouseId) {
    const currentWarehouseOutbounds = this.data.outboundList.filter(
      (item) => item.warehouse_id === warehouseId
    );

    if (currentWarehouseOutbounds.length > 0) {
      const finalInventoryList = this.calculateFinalInventoryStateNew(
        this.data.inventoryList,
        currentWarehouseOutbounds
      );
      this.setData({
        inventoryList: finalInventoryList,
      });
    }
  },

  /**
   * 清空购物车
   */
  clearCart() {
    wx.showModal({
      title: "确认清空",
      content: "确定要清空待出库清单吗？",
      success: (res) => {
        if (res.confirm) {
          // 重置库存显示到原始状态
          const resetInventoryList = this.data.inventoryList.map((item) => ({
            ...item,
            availablePackageCount: item.package_count,
            availableTotalQuantity: item.total_quantity,
            selectedPackageQuantity: 0,
            calculatedPackageQuantity:
              item.contents.length > 1 ? "0.00" : undefined,
            contents: item.contents.map((content) => ({
              ...content,
              selectedQuantity: 0,
            })),
          }));
          this.setData({ inventoryList: resetInventoryList });

          this.setData({
            outboundList: [],
            groupedCart: [],
            cartTotalPackages: 0,
            cartTotalPieces: 0,
            showCart: false,
          });

          wx.showToast({
            title: "已清空",
            icon: "success",
          });
        }
      },
    });
  },
});
