// pages/calendar-logs/calendar-logs.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 查询参数
    queryParams: {
      start_date: '',
      end_date: '',
      warehouse_id: '',
      clothing_id: '',
      oem_clothing_id: '',
      operation_type: 'outbound',
      isOem: false
    },

    // 日志查询
    logQuery: {
      operation_type: "outbound",
      operation_type_text: "出库",
    },

    // 日志数据
    logsList: [],
    loadingLogs: false,
    hasMoreLogs: true,
    logsPage: 1,
    lastApiResponse: null,

    // 页面来源信息
    pageSource: '', // 'warehouse-main' | 'warehouse-detail' | 'clothing-detail'

    // 操作类型选择器
    showOperationDropdown: false,
    operationTypes: [
      { name: '全部', value: '' },
      { name: '出库', value: 'outbound' },
      { name: '入库', value: 'inbound' },
      { name: '移库', value: 'transfer' },
      { name: '盘点', value: 'inventory' }
    ],

    // 页面标题信息
    pageTitle: '日志查询',

    // 页面来源相关信息
    warehouseName: '', // 仓库名称（从仓库详情页进入时显示）
    clothingName: '', // 服装名称（从服装详情页进入时显示）
    loadingSourceInfo: false, // 加载来源信息状态

    // 服装信息卡片显示
    showClothingCard: false,
    selectedClothingInfo: null,
    isOemClothing: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('日历日志页面加载，参数:', options);

    // 解析查询参数
    const queryParams = {
      start_date: options.start_date || '',
      end_date: options.end_date || '',
      warehouse_id: options.warehouse_id || '',
      clothing_id: options.clothing_id || '',
      oem_clothing_id: options.oem_clothing_id || '',
      operation_type: options.operation_type || 'outbound',
      isOem: options.isOem === 'true' // 解析isOem参数
    };

    // 判断页面来源
    let pageSource = 'warehouse-main'; // 默认为仓库主页面
    if (queryParams.warehouse_id && !queryParams.clothing_id && !queryParams.oem_clothing_id) {
      pageSource = 'warehouse-detail';
    } else if (queryParams.clothing_id || queryParams.oem_clothing_id) {
      pageSource = 'clothing-detail';
    }

    // 设置页面标题和日期范围文本
    let pageTitle = '日志查询';

    if (queryParams.start_date && queryParams.end_date) {
      if (queryParams.start_date === queryParams.end_date) {
        pageTitle = `${queryParams.start_date} `;
      } else {
        pageTitle = `${queryParams.start_date} 至 ${queryParams.end_date} `;
      }
    }

    // 设置操作类型文本
    const operationType = this.data.operationTypes.find(type => type.value === queryParams.operation_type);
    const logQuery = {
      ...this.data.logQuery,
      operation_type: queryParams.operation_type,
      operation_type_text: operationType ? operationType.name : '全部'
    };

    this.setData({
      queryParams,
      logQuery,
      pageTitle,
      pageSource
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: pageTitle
    });

    // 根据页面来源加载相关信息
    this.loadSourceInfo();

    // 加载日志数据
    this.loadLogs(true);
  },

  /**
   * 加载来源信息（仓库名称或服装名称）
   */
  async loadSourceInfo() {
    const { pageSource, queryParams } = this.data;
    console.log('=== 开始加载来源信息 ===');
    console.log('页面来源:', pageSource);
    console.log('查询参数:', queryParams);

    if (pageSource === 'warehouse-detail' && queryParams.warehouse_id) {
      // 从仓库详情页进入，获取仓库名称
      this.setData({ loadingSourceInfo: true });
      try {
        console.log('正在获取仓库详情，warehouse_id:', queryParams.warehouse_id);
        const response = await Api.getWarehouseDetail({ warehouse_id: queryParams.warehouse_id });
        console.log('仓库详情API响应:', response);

        if (response.data && response.data.code === 200) {
          const warehouseName = response.data.data.name || '未知仓库';
          console.log('获取到仓库名称:', warehouseName);
          this.setData({
            warehouseName: warehouseName
          });
        } else {
          console.error('获取仓库详情失败，响应码:', response.data?.code);
          this.setData({
            warehouseName: '获取失败'
          });
        }
      } catch (error) {
        console.error('获取仓库信息失败:', error);
        this.setData({
          warehouseName: '获取失败'
        });
      } finally {
        this.setData({ loadingSourceInfo: false });
      }
    } else if (pageSource === 'clothing-detail' && (queryParams.clothing_id || queryParams.oem_clothing_id)) {
      // 从服装详情页进入，获取服装名称
      this.setData({ loadingSourceInfo: true });
      try {
        console.log('正在获取服装详情，clothing_id:', queryParams.clothing_id, 'oem_clothing_id:', queryParams.oem_clothing_id, 'isOem:', queryParams.isOem);

        let response;
        let clothingName = '';

        if (queryParams.oem_clothing_id || queryParams.isOem) {
          // 直接调用OEM服装API
          console.log('调用OEM服装API...');
          const oemClothingId = queryParams.oem_clothing_id || queryParams.clothing_id;
          response = await Api.getOemClothingInfo({ oem_clothing_id: oemClothingId });
          console.log('OEM服装API响应:', response);
          if (response.data && response.data.oem_clothing_name) {
            clothingName = response.data.oem_clothing_name;
            console.log('成功获取OEM服装名称:', clothingName);
          }
        } else if (queryParams.clothing_id) {
          // 直接调用普通服装API
          console.log('调用普通服装API...');
          response = await Api.getClothingInfo({ clothing_id: queryParams.clothing_id });
          console.log('普通服装API响应:', response);
          if (response.data && response.data.clothing_name) {
            clothingName = response.data.clothing_name;
            console.log('成功获取普通服装名称:', clothingName);
          }
        }

        // 设置结果
        if (clothingName) {
          this.setData({
            clothingName: clothingName
          });
        } else {
          console.error('获取服装名称失败，API返回数据不完整');
          this.setData({
            clothingName: '获取失败'
          });
        }

      } catch (error) {
        console.error('获取服装信息失败:', error);
        this.setData({
          clothingName: '获取失败'
        });
      } finally {
        this.setData({ loadingSourceInfo: false });
      }
    }
  },

  /**
   * 加载日志数据
   */
  async loadLogs(reset = false) {
    if (this.data.loadingLogs || (!this.data.hasMoreLogs && !reset)) return;

    try {
      this.setData({ loadingLogs: true });

      const { queryParams, logQuery, logsPage } = this.data;
      const currentPage = reset ? 1 : logsPage;

      // 构建查询参数
      const params = {
        page: currentPage,
        limit: 20,
        start_date: queryParams.start_date,
        end_date: queryParams.end_date,
        operation_type: logQuery.operation_type,
        log_type: 'detail' // 固定使用明细模式
      };

      // 如果有仓库ID，添加到参数中
      if (queryParams.warehouse_id) {
        params.warehouse_id = queryParams.warehouse_id;
      }

      // 如果有服装ID，添加到参数中
      if (queryParams.clothing_id) {
        params.clothing_id = queryParams.clothing_id;
      }

      // 如果有OEM服装ID，添加到参数中
      if (queryParams.oem_clothing_id) {
        params.oem_clothing_id = queryParams.oem_clothing_id;
      }

      console.log('=== 开始加载操作日志明细 ===');
      console.log('API调用参数:', params);
      console.log('使用的API方法: getWarehouseOperationLogsDetail');

      const response = await Api.getWarehouseOperationLogsDetail(params);
      console.log('=== API响应结果 ===');
      console.log('响应状态:', response?.data?.code);
      console.log('响应数据:', response?.data?.data);
      console.log('完整响应:', response);

      if (response.data.code === 200) {
        const newLogs = response.data.data.list || [];
        const hasMore = newLogs.length === params.limit;

        // 调试：打印第一条日志的结构
        if (newLogs.length > 0) {
          console.log('=== 第一条日志数据结构 ===');
          console.log('完整日志对象:', newLogs[0]);
          console.log('contents_changes:', newLogs[0].contents_changes);
          console.log('根级别字段:', {
            product_name: newLogs[0].product_name,
            clothing_name: newLogs[0].clothing_name,
            quantity_change: newLogs[0].quantity_change,
            package_change: newLogs[0].package_change,
            warehouse_name: newLogs[0].warehouse_name,
            warehouse_id: newLogs[0].warehouse_id
          });
        }

        this.setData({
          logsList: reset ? newLogs : [...this.data.logsList, ...newLogs],
          hasMoreLogs: hasMore,
          logsPage: currentPage + 1,
          lastApiResponse: response.data.data
        });
      } else {
        console.error('加载日志失败:', response.data.message);
        wx.showToast({
          title: response.data.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载日志异常:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({ loadingLogs: false });
    }
  },

  /**
   * 加载更多日志
   */
  loadMoreLogs() {
    this.loadLogs();
  },

  /**
   * 切换操作类型下拉框
   */
  toggleOperationDropdown() {
    this.setData({
      showOperationDropdown: !this.data.showOperationDropdown
    });
  },

  /**
   * 选择操作类型
   */
  selectOperationType(e) {
    const operation = e.currentTarget.dataset.operation;

    this.setData({
      'logQuery.operation_type': operation.value,
      'logQuery.operation_type_text': operation.name,
      showOperationDropdown: false,
      logsList: [],
      logsPage: 1,
      hasMoreLogs: true,
      lastApiResponse: null
    });

    this.loadLogs(true);
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 点击服装名称显示服装卡片
   */
  async onClothingNameTap(e) {
    const item = e.currentTarget.dataset.item;
    const change = e.currentTarget.dataset.change;
    console.log('点击服装名称:', { item, change });

    // 优先使用change数据，如果没有则使用item数据
    const targetData = change || item;

    if (!targetData || (!targetData.sku && !targetData.clothing_id && !targetData.oem_clothing_id)) {
      wx.showToast({
        title: '服装信息不完整',
        icon: 'none'
      });
      return;
    }

    // 显示加载状态
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      let clothingInfo;
      let isOemClothing = false;

      console.log('=== 开始获取服装详情 ===');
      console.log('目标数据:', targetData);

      // 优先使用clothing_id或oem_clothing_id
      if (targetData.oem_clothing_id) {
        console.log('使用oem_clothing_id获取OEM服装详情:', targetData.oem_clothing_id);
        isOemClothing = true;
        const response = await Api.getOemClothingInfo({ oem_clothing_id: targetData.oem_clothing_id });
        console.log('OEM服装详情API响应:', response);
        if (response.data) {
          clothingInfo = response.data;
        }
      } else if (targetData.clothing_id) {
        console.log('使用clothing_id获取服装详情:', targetData.clothing_id);
        const response = await Api.getClothingInfo({ clothing_id: targetData.clothing_id });
        console.log('服装详情API响应:', response);
        if (response.data) {
          clothingInfo = response.data;
        }
      } else if (targetData.sku) {
        console.log('使用SKU获取服装详情:', targetData.sku);
        // 根据SKU规则判断服装类型：如果SKU包含"_"且前面是"oem"，则为OEM服装
        const sku = targetData.sku;
        isOemClothing = sku && sku.includes('_') && sku.split('_')[0].toLowerCase() === 'oem';
        console.log('根据SKU判断服装类型:', { sku, isOemClothing });

        if (isOemClothing) {
          // 获取OEM服装详情 - 从SKU中提取OEM服装ID
          const oemClothingId = sku.split('_')[1];
          const response = await Api.getOemClothingInfo({ oem_clothing_id: oemClothingId });
          console.log('OEM服装详情API响应:', response);
          if (response.data) {
            clothingInfo = response.data;
          }
        } else {
          // 获取本厂服装详情
          const clothingId = targetData.clothing_id || sku.split('_')[1];
          const response = await Api.getClothingInfo({ clothing_id: clothingId });
          console.log('服装详情API响应:', response);
          if (response.data) {
            clothingInfo = response.data;
          }
        }
      }

      console.log('=== 最终获取的服装信息 ===');
      console.log('clothingInfo:', clothingInfo);
      console.log('isOemClothing:', isOemClothing);

      if (clothingInfo) {
        this.setData({
          selectedClothingInfo: clothingInfo,
          isOemClothing: isOemClothing,
          showClothingCard: true
        });
        console.log('服装卡片数据设置完成');
      } else {
        console.error('未获取到服装信息');
        wx.showToast({
          title: '获取服装信息失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取服装详情失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 关闭服装信息卡片
   */
  onCloseClothingCard() {
    this.setData({
      showClothingCard: false,
      selectedClothingInfo: null,
      isOemClothing: false
    });
  },

  /**
   * 重置查询
   */
  onResetLogs() {
    const operationType = this.data.operationTypes[1]; // 默认选择出库

    this.setData({
      'logQuery.operation_type': operationType.value,
      'logQuery.operation_type_text': operationType.name,
      showOperationDropdown: false,
      logsList: [],
      logsPage: 1,
      hasMoreLogs: true,
      lastApiResponse: null
    });

    this.loadLogs(true);
  },

  /**
   * 处理swipe-cell点击事件
   */
  onSwipeCellClick(e) {
    const position = e.detail;
    console.log("swipe-cell点击位置:", position);
    // 如果点击的是外部区域，关闭所有打开的swipe-cell
    if (position === "outside" || position === "cell") {
      // swipe-cell组件会自动处理关闭逻辑
    }
  },

  /**
   * 删除日志（冲正）
   */
  onDeleteLog(e) {
    const logId = e.currentTarget.dataset.logId;
    const logItem = e.currentTarget.dataset.logItem;

    console.log("删除日志:", logId, logItem);

    wx.showModal({
      title: "确认删除",
      content: `确定要删除这条${
        logItem.type === 'inbound' ? '入库' :
        logItem.type === 'outbound' ? '出库' :
        logItem.type === 'transfer_out' ? '移出' :
        logItem.type === 'transfer_in' ? '移入' :
        logItem.type === 'inventory_surplus' ? '盘盈' :
        logItem.type === 'inventory_deficit' ? '盘亏' :
        logItem.type
      }日志吗？此操作将进行冲正处理，不可撤销。`,
      confirmText: "确认删除",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          this.executeDeleteLog(logId);
        }
      },
    });
  },

  /**
   * 执行删除日志操作
   */
  async executeDeleteLog(logId) {
    try {
      wx.showLoading({
        title: "处理中...",
        mask: true,
      });

      const response = await Api.reverseOperationLog({
        log_id: logId,
        operator_name: "小程序用户", // 可以从用户信息中获取
      });

      if (response.data && response.data.code === 200) {
        wx.showToast({
          title: "删除成功",
          icon: "success",
        });

        // 重新加载日志数据
        this.loadLogs(true);
      } else {
        wx.showToast({
          title: response.data?.message || "删除失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("删除日志失败:", error);
      wx.showToast({
        title: "删除失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  }
});
