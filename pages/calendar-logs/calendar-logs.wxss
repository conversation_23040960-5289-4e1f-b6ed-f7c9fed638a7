/* pages/calendar-logs/calendar-logs.wxss */

.page-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20rpx;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 24rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.header-title {
  align-items: center;
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
}

.header-subtitle {
  font-size: 28rpx;
  color: #666;
  background: #f0f9f6;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  display: inline-block;
}

/* 日志查询区域 */
.logs-section {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.search-header {
  background: #f8f9fa;
  padding: 32rpx 40rpx;
  border-bottom: 2rpx solid #e9ecef;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex-wrap: wrap;
}

.search-form-item {
  position: relative;
}

/* 来源信息显示样式 */
.source-info-item {
  display: flex;
  align-items: center;
  background: #f0f9f6;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  border: 2rpx solid #2c9678;
  max-width: 400rpx;
}

.source-info-label {
  font-size: 26rpx;
  color: #2c9678;
  font-weight: 600;
  white-space: nowrap;
}

.source-info-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 300rpx;
}

.source-info-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60rpx;
}

/* 操作类型选择器 */
.operation-type-selector {
  position: relative;
  min-width: 240rpx;
}

.selector-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  background: white;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selector-trigger.active {
  border-color: #2c9678;
  box-shadow: 0 0 0 4rpx rgba(44, 150, 120, 0.1);
}

.selector-content {
  flex: 1;
}

.selector-text {
  font-size: 28rpx;
  color: #333;
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 8rpx;
}

.dropdown-content {
  background: white;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.dropdown-option {
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-option:last-child {
  border-bottom: none;
}

.dropdown-option:hover {
  background: #f8f9fa;
}

.dropdown-option.selected {
  background: #f0f9f6;
}

.option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.selected-indicator {
  margin-left: 16rpx;
}

/* 日志列表 */
.logs-list {
  height: calc(100vh - 180rpx);
  padding: 0;
}

.empty-state {
  padding: 120rpx 40rpx;
  text-align: center;
}

.log-item {
  border-bottom: 2rpx solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item:hover {
  background: #fafafa;
}

/* 紧凑布局样式 */
.log-item-content-compact {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  gap: 16rpx;
  min-height: 40rpx;
}

/* 左侧区域：服装信息 */
.left-section {
  flex: 2;
  min-width: 0;
}

.clothing-info-compact {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 4rpx;
}

.clothing-info-compact:last-child {
  margin-bottom: 0;
}

.clothing-name-compact {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
  line-height: 1.3;
  max-width: 240rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.clothing-name-compact.clickable {
  color: #2c9678;
  cursor: pointer;
}

.quantity-compact {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.package-compact {
  color: #999;
  font-size: 22rpx;
  margin-left: 4rpx;
}

/* 中间区域：仓库信息 */
.middle-section {
  flex: 1;
  min-width: 0;
}

.warehouse-compact {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 160rpx;
}

/* 右侧区域：日期和标签 */
.right-section {
  flex: 2;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  text-align: center;
}

.date-compact {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.bottom-text {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  margin-top: 20rpx;
  padding-bottom: 20rpx;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  gap: 16rpx;
  color: #666;
  font-size: 28rpx;
}

/* 右滑删除按钮样式 */
.delete-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 130rpx;
  height: 100%;
  background-color: #c45a65;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 0;
  transition: background-color 0.2s ease;
}

.delete-button:active {
  background-color: #a94952;
}

/* 按钮样式 */
.reset-form-btn {
  background: #6c757d !important;
  color: white !important;
  border: none !important;
  border-radius: 12rpx !important;
  font-size: 24rpx !important;
  padding: 16rpx 32rpx !important;
  height: 70rpx !important;
}

/* 服装信息卡片弹窗样式 */
.clothing-card-popup {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  max-height: 80vh;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: #f8f9fa;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.popup-content {
  padding: 32rpx;
  max-height: calc(80vh - 120rpx);
  overflow-y: auto;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .page-container {
    padding: 16rpx;
  }

  .search-form {
    gap: 16rpx;
  }

  .source-info-item {
    max-width: 300rpx;
    padding: 12rpx 20rpx;
  }

  .source-info-text {
    max-width: 200rpx;
    font-size: 24rpx;
  }

  .log-item-content-compact {
    padding: 12rpx 20rpx;
    gap: 12rpx;
  }

  .operation-type-selector {
    min-width: 180rpx;
  }

  .clothing-card-popup {
    max-height: 85vh;
  }

  .popup-content {
    padding: 24rpx;
    max-height: calc(85vh - 120rpx);
  }

  .logs-list {
    height: calc(100vh - 300rpx);
  }
}
