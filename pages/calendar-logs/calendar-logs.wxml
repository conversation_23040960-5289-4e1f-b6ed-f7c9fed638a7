<!-- pages/calendar-logs/calendar-logs.wxml -->
<view class="page-container">
  <!-- 日志查询区域 -->
  <view class="logs-section">
    <view class="search-header">
      <view class="search-form">
        <!-- 来源信息显示 -->
        <view wx:if="{{ pageSource === 'warehouse-detail' }}" class="search-form-item source-info-item">
          <view class="source-info-label">仓库：</view>
          <view wx:if="{{ loadingSourceInfo }}" class="source-info-loading">
            <van-loading type="spinner" size="16px" />
          </view>
          <view wx:else class="source-info-text">{{ warehouseName || '加载中...' }}</view>
        </view>
        <view wx:elif="{{ pageSource === 'clothing-detail' }}" class="search-form-item source-info-item">
          <view class="source-info-label">服装：</view>
          <view wx:if="{{ loadingSourceInfo }}" class="source-info-loading">
            <van-loading type="spinner" size="16px" />
          </view>
          <view wx:else class="source-info-text">{{ clothingName || '加载中...' }}</view>
        </view>
        <!-- 操作类型选择器 -->
        <view class="search-form-item">
          <view class="operation-type-selector" catchtap="stopPropagation">
            <view class="selector-trigger {{showOperationDropdown ? 'active' : ''}}" bind:tap="toggleOperationDropdown">
              <view class="selector-content">
                <view class="selector-text">{{ logQuery.operation_type_text || '全部' }}</view>
              </view>
              <van-icon name="{{showOperationDropdown ? 'arrow-up' : 'arrow-down'}}" size="14px" color="#999" />
            </view>
            <!-- 下拉选项 -->
            <view wx:if="{{showOperationDropdown}}" class="dropdown-options">
              <view class="dropdown-content">
                <view wx:for="{{operationTypes}}" wx:key="value" class="dropdown-option {{item.value === logQuery.operation_type ? 'selected' : ''}}" data-operation="{{item}}" bind:tap="selectOperationType">
                  <view class="option-content">
                    <view class="option-text">{{item.name}}</view>
                    <!-- 选中状态指示器 -->
                    <view wx:if="{{item.value === logQuery.operation_type}}" class="selected-indicator">
                      <van-icon name="success" color="#07c160" size="16px" />
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 重置按钮 -->
        <view class="search-form-item">
          <van-button size="small" bind:click="onResetLogs" custom-class="reset-form-btn">
            重置
          </van-button>
        </view>
      </view>
    </view>
    <!-- 日志列表 -->
    <scroll-view class="logs-list" scroll-y bindscrolltolower="loadMoreLogs" lower-threshold="100">
      <view wx:if="{{logsList.length === 0 && !loadingLogs}}" class="empty-state">
        <van-empty description="暂无日志数据" />
      </view>
      <view wx:else>
        <view wx:for="{{logsList}}" wx:key="index" class="log-item">
          <!-- 使用van-swipe-cell实现右滑删除 -->
          <van-swipe-cell right-width="{{ 70 }}" name="{{item.id}}" bind:click="onSwipeCellClick">
            <!-- 紧凑的单行布局 -->
            <view class="log-item-content-compact">
              <!-- 左侧：服装信息和数量 -->
              <view class="left-section">
                <!-- 服装名称和数量信息 -->
                <view wx:if="{{ item.contents_changes && item.contents_changes.length > 0 }}">
                  <view wx:for="{{ item.contents_changes }}" wx:for-item="change" wx:for-index="changeIndex" wx:key="changeIndex" class="clothing-info-compact">
                    <view wx:if="{{ pageSource !== 'clothing-detail' }}" class="clothing-name-compact clickable" data-item="{{item}}" data-change="{{change}}" bindtap="onClothingNameTap">
                      {{ change.product_name || change.clothing_name || '未知服装' }}
                    </view>
                    <view class="quantity-compact">
                      {{ change.quantity_change > 0 ? change.quantity_change : -change.quantity_change }}件
                      <view wx:if="{{ change.package_change }}" class="package-compact">
                        ({{ change.package_change > 0 ? change.package_change : -change.package_change }}包)
                      </view>
                    </view>
                  </view>
                </view>
                <view wx:else class="clothing-info-compact">
                  <view wx:if="{{ pageSource !== 'clothing-detail' }}" class="clothing-name-compact clickable" data-item="{{item}}" bindtap="onClothingNameTap">
                    {{ item.product_name || item.clothing_name || '未知服装' }}
                  </view>
                  <view class="quantity-compact">
                    {{ item.quantity_change > 0 ? item.quantity_change : -item.quantity_change }}件
                    <text wx:if="{{ item.outbound_package_count }}" class="package-compact">
                      ({{ item.outbound_package_count }}包)
                    </text>
                    <view wx:elif="{{ item.package_change }}" class="package-compact">
                      ({{ item.package_change > 0 ? item.package_change : -item.package_change }}包)
                    </view>
                  </view>
                </view>
              </view>
              <!-- 中间：仓库信息 -->
              <view wx:if="{{ pageSource !== 'warehouse-detail' }}" class="middle-section">
                <view wx:if="{{ item.type === 'transfer_out' || item.type === 'transfer_in' }}" class="warehouse-compact">
                  {{ item.type === 'transfer_out' ? '移出至:' : '移入自:' }}{{ item.transfer_notes || item.target_warehouse_name || item.source_warehouse_name || '--' }}
                </view>
                <view wx:else class="warehouse-compact">{{ item.warehouse_name || '--' }}</view>
              </view>
              <!-- 右侧：日期和操作标签 -->
              <view class="right-section">
                <view class="date-compact">{{ item.date }}</view>
                <van-tag color="{{ item.type === 'inbound' ? '#5698c3' : item.type === 'outbound' ? '#2c9678' : (item.type === 'transfer_out' || item.type === 'transfer_in') ? '#ddc871' : '#806d9e' }}" size="small">
                  {{ item.type === 'inbound' ? '入库' : item.type === 'outbound' ? '出库' : item.type === 'transfer_out'? '移出' :item.type === 'transfer_in' ? '移入' : item.type === 'inventory_surplus' ? '盘盈' : item.type === 'inventory_deficit' ? '盘亏' : item.type }}
                </van-tag>
              </view>
            </view>
            <!-- 右滑删除按钮 -->
            <view slot="right" class="delete-button" data-log-id="{{item.id}}" data-log-item="{{item}}" bindtap="onDeleteLog">
              删除
            </view>
          </van-swipe-cell>
        </view>
        <view class="bottom-text">到底了...</view>
      </view>
      <view wx:if="{{loadingLogs}}" class="loading-more">
        <van-loading type="spinner" size="20px" />
        <text>加载中...</text>
      </view>
    </scroll-view>
  </view>
  <!-- 服装信息卡片弹窗 -->
  <van-popup show="{{ showClothingCard }}" position="center" round bind:close="onCloseClothingCard" custom-style="width: 90%; max-width: 400px;">
    <view class="clothing-card-popup">
      <view class="popup-header">
        <view class="popup-title">服装详情</view>
        <van-icon name="cross" size="20px" color="#999" bind:click="onCloseClothingCard" />
      </view>
      <view class="popup-content">
        <clothing-info-card wx:if="{{ selectedClothingInfo }}" clothingInfo="{{ isOemClothing ? null : selectedClothingInfo }}" oemClothingInfo="{{ isOemClothing ? selectedClothingInfo : null }}" isOem="{{ isOemClothing }}" showDetailInfo="{{ true }}" disableNavigation="{{ false }}" />
      </view>
    </view>
  </van-popup>
</view>