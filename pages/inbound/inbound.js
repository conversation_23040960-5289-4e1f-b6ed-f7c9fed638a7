// pages/inbound/inbound.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 货运信息
    transportationInfo: null,
    // 到货明细列表
    arrivalDetails: [],
    // 仓库列表
    warehouseList: [],
    // 当前选中的仓库
    selectedWarehouse: null,
    // 预入库购物车
    inboundCart: [],
    // 按仓库分组的购物车数据
    groupedCart: [],
    // 购物车统计
    cartTotalPackages: 0,
    cartTotalPieces: 0,
    // 显示购物车浮窗
    showCart: false,
    // 加载状态
    loading: false,
    // 统计数据
    totalPackages: 0,
    totalPieces: 0,
    // 是否显示核对按钮
    showConfirmButton: false,
    // 服装信息弹窗相关
    showClothingInfo: false,
    selectedClothingInfo: null,
    isOemClothing: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { transportation_id, transportationData } = options;

    // 优先使用从货运详情页面传递的完整数据
    if (transportationData) {
      try {
        const transportationInfo = JSON.parse(
          decodeURIComponent(transportationData)
        );
        this.loadTransportationDetail(transportationInfo.transportation_id);
        this.loadWarehouses();
      } catch (error) {
        console.error("解析货运数据失败:", error);
        wx.showToast({
          title: "数据解析失败",
          icon: "none",
        });
        wx.navigateBack();
      }
    } else if (transportation_id) {
      this.loadTransportationDetail(transportation_id);
      this.loadWarehouses();
    } else {
      wx.showToast({
        title: "缺少货运信息",
        icon: "none",
      });
      wx.navigateBack();
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新仓库列表，以防从仓库详情页面返回时有新增或修改
    this.loadWarehouses();
  },

  /**
   * 处理入库明细数据：扁平化结构，排除已入库的包裹
   */
  processInboundDetails(details) {
    if (!details || details.length === 0) {
      return [];
    }
    console.log("原始入库明细数据:", details);

    // 扁平化处理：每个服装作为独立项，按transportation_id+series_number分组
    const processedData = [];
    let index = 0;

    // 按transportation_id+series_number分组
    const groupedByPackage = {};
    details.forEach((item) => {
      const packageKey = `${item.transportation_id}_${item.series_number || 0}`;
      if (!groupedByPackage[packageKey]) {
        groupedByPackage[packageKey] = [];
      }
      groupedByPackage[packageKey].push(item);
    });

    // 转换为扁平化格式
    Object.keys(groupedByPackage)
      .sort()
      .forEach((packageKey) => {
        const items = groupedByPackage[packageKey];

        if (items.length > 0) {
          const firstItem = items[0];

          // 使用后端返回的剩余可入库数量
          const remainingQuantity = firstItem.remainingQuantity || 0;
          const originalPackageQuantity = firstItem.package_quantity || 0;
          const inboundPackages = firstItem.inbound_packages || 0;
          const QUP = firstItem.QUP || 1;

          // 只有剩余数量大于0的包裹才显示
          if (remainingQuantity > 0) {
            // 创建扁平化的包裹项
            processedData.push({
              index: index,
              transportation_id: firstItem.transportation_id,
              series_number: firstItem.series_number || 0,
              QUP: QUP,
              package_quantity: originalPackageQuantity, // 原始包裹数
              remainingQuantity: remainingQuantity, // 剩余可入库数量
              inboundPackages: inboundPackages, // 已入库包裹数
              selectedQuantity: 0,
              // 服装列表（用于显示）
              clothingItems: items.map((item) => ({
                clothing_id: item.clothing_id,
                clothing_name: item.clothing_name,
                oem: item.oem,
                out_pcs: item.out_pcs,
                img: item.img || [],
              })),
              // 扁平化数据，用于后续入库操作
              flatItems: items.map((item) => ({
                clothing_id: item.clothing_id,
                clothing_name: item.clothing_name,
                oem: item.oem,
                out_pcs: item.out_pcs,
                pieces_per_package: QUP,
                transportation_id: item.transportation_id,
                series_number: item.series_number,
                supplier: item.supplier,
              })),
            });

            index++;
          }
        }
      });

    console.log("处理后的入库明细数据:", processedData);
    return processedData;
  },

  /**
   * 加载货运详情
   */
  async loadTransportationDetail(transportationId) {
    try {
      // this.setData({ loading: true });

      const response = await Api.getInboundTransportationDetail({
        id: transportationId,
      });
      console.log("货运详情API响应:", response);

      if (response.data && response.data.code === 200) {
        const transportationInfo = response.data.data;
        const arrivalDetails = response.data.data.detail || [];
        console.log("原始入库明细数据987:", arrivalDetails);

        // 后端已经处理了分批入库逻辑，直接按series_number分组显示
        const processedDetails = this.processInboundDetails(arrivalDetails);
        console.log("处理后的入库明细数据:", processedDetails);

        // 格式化发货日期
        const formattedTransportation = {
          ...transportationInfo,
          date_out: this.formatDate(transportationInfo.date_out),
        };

        this.setData({
          transportationInfo: formattedTransportation,
          arrivalDetails: processedDetails,
        });

        console.log("设置的货运信息:", formattedTransportation);
        console.log("货运信息中的total_package_quantity:", formattedTransportation.total_package_quantity);
        console.log("货运信息中的total_pcs:", formattedTransportation.total_pcs);
        console.log("设置的到货明细:", JSON.stringify(processedDetails));

        this.calculateTotalStats();
      } else {
        console.error("API返回错误:", response);
        wx.showToast({
          title: response.data?.message || "加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载货运详情失败:", error);
      wx.showToast({
        title: "加载失败",
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 加载仓库列表
   */
  async loadWarehouses() {
    try {
      const response = await Api.getWarehouseList({ status: "active" });

      if (response.data.code === 200) {
        const warehouseList = response.data.data.list || [];

        // 如果当前没有选中仓库，或者选中的仓库不在新列表中，则选择第一个
        let selectedWarehouse = this.data.selectedWarehouse;
        if (!selectedWarehouse || !warehouseList.find(w =>
          (w.warehouse_id === selectedWarehouse.warehouse_id) ||
          (w.id === selectedWarehouse.id)
        )) {
          selectedWarehouse = warehouseList[0] || null;
        }

        this.setData({
          warehouseList: warehouseList,
          selectedWarehouse: selectedWarehouse,
        });
      }
    } catch (error) {
      console.error("加载仓库列表失败:", error);
    }
  },

  /**
   * 恢复选中的仓库（从仓库详情页面返回时使用）
   */
  restoreSelectedWarehouse(warehouseId) {
    const { warehouseList } = this.data;
    const targetWarehouse = warehouseList.find(warehouse =>
      warehouse.warehouse_id === warehouseId || warehouse.id === warehouseId
    );

    if (targetWarehouse) {
      this.setData({
        selectedWarehouse: targetWarehouse
      });
    }
  },

  /**
   * 清除选中的仓库（删除仓库时使用）
   */
  clearSelectedWarehouse() {
    this.setData({
      selectedWarehouse: null
    });
  },

  /**
   * 格式化日期 - 只显示日期部分
   */
  formatDate(dateString) {
    if (!dateString) return "";

    try {
      // 如果是字符串，尝试分割
      if (typeof dateString === "string") {
        // 处理 "2024-01-01 12:00:00" 格式
        if (dateString.includes(" ")) {
          return dateString.split(" ")[0];
        }
        // 处理 "2024-01-01T12:00:00" 格式
        if (dateString.includes("T")) {
          return dateString.split("T")[0];
        }
        return dateString;
      }

      // 如果是Date对象
      if (dateString instanceof Date) {
        return dateString.toISOString().split("T")[0];
      }

      return dateString;
    } catch (error) {
      console.error("日期格式化失败:", error);
      return dateString;
    }
  },

  /**
   * 计算总统计（到货总数，不是剩余数量）
   */
  calculateTotalStats() {
    const { arrivalDetails } = this.data;
    let totalPackages = 0;
    let totalPieces = 0;

    arrivalDetails.forEach((detail) => {
      // 使用package_quantity计算到货总包数，而不是remainingQuantity
      totalPackages += detail.package_quantity || 0;
      // 使用flatItems计算总件数
      detail.flatItems.forEach((item) => {
        totalPieces += item.out_pcs || 0;
      });
    });

    this.setData({
      totalPackages,
      totalPieces,
    });
  },

  /**
   * 选择仓库
   */
  onSelectWarehouse(e) {
    const { warehouse } = e.currentTarget.dataset;
    this.setData({
      selectedWarehouse: warehouse,
    });
  },

  /**
   * 计算指定明细项的最大可入库数量
   */
  calculateMaxQuantityForDetail(detailIndex, excludeCartKey = null) {
    const { arrivalDetails, inboundCart } = this.data;
    const detail = arrivalDetails[detailIndex];

    if (!detail) return 0;

    // 计算该明细项在购物车中已入库的总数量（排除当前正在编辑的项）
    const alreadyInboundQuantity = inboundCart
      .filter(
        (item) =>
          item.detail_index === detailIndex && item.cart_key !== excludeCartKey
      )
      .reduce((sum, item) => sum + item.inbound_quantity, 0);

    // 最大可入库数量 = 原始包裹数量 - 已入库数量
    return Math.max(0, detail.package_quantity - alreadyInboundQuantity);
  },

  /**
   * 数量变化（步进器）
   */
  onQuantityChange(e) {
    const { index } = e.currentTarget.dataset;
    const quantity = parseInt(e.detail) || 0;

    const arrivalDetails = this.data.arrivalDetails;
    arrivalDetails[index].selectedQuantity = quantity;

    this.setData({
      arrivalDetails: arrivalDetails,
    });
  },

  /**
   * 点击明细项的入库按钮
   */
  onInboundTap(e) {
    const { detail, index } = e.currentTarget.dataset;

    if (!this.data.selectedWarehouse) {
      wx.showToast({
        title: "请先选择仓库",
        icon: "none",
      });
      return;
    }

    if (!detail.selectedQuantity || detail.selectedQuantity <= 0) {
      wx.showToast({
        title: "请选择入库数量",
        icon: "none",
      });
      return;
    }

    this.addToInboundCart(detail, index);
  },

  /**
   * 添加到入库购物车
   */
  addToInboundCart(detail, detailIndex) {
    const { selectedWarehouse, inboundCart, arrivalDetails } = this.data;
    const inboundQuantity = detail.selectedQuantity;

    // 生成唯一的购物车项标识 - 基于仓库ID和明细索引
    const cartKey = `${selectedWarehouse.warehouse_id}_${detailIndex}`;

    // 检查是否已经在购物车中（基于仓库ID和明细索引）
    const existingIndex = inboundCart.findIndex(
      (item) => item.cart_key === cartKey
    );

    // 验证入库数量是否超过可用数量
    const maxAvailable = this.calculateMaxQuantityForDetail(
      detailIndex,
      cartKey
    );

    if (inboundQuantity > maxAvailable) {
      wx.showToast({
        title: `该包裹最多还能入库${maxAvailable}包`,
        icon: "none",
      });
      return;
    }

    let newCart;
    if (existingIndex >= 0) {
      // 更新数量
      newCart = inboundCart.map((item, index) => {
        if (index === existingIndex) {
          return {
            ...item,
            inbound_quantity: item.inbound_quantity + inboundQuantity,
            total_inbound_pieces:
              (item.inbound_quantity + inboundQuantity) * (detail.QUP || 1),
          };
        }
        return item;
      });
    } else {
      // 添加新项
      const cartItem = {
        cart_key: cartKey, // 添加唯一标识
        detail_index: detailIndex, // 保存明细索引
        // 从flatItems中获取服装信息，合并所有服装名称
        clothing_name:
          detail.flatItems && detail.flatItems.length > 0
            ? detail.flatItems.map((item) => item.clothing_name).join("、")
            : "未知服装",
        clothing_id:
          detail.flatItems && detail.flatItems[0]
            ? detail.flatItems[0].clothing_id
            : "",
        warehouse_id: selectedWarehouse.warehouse_id,
        warehouse_name: selectedWarehouse.name,
        inbound_quantity: inboundQuantity,
        total_inbound_pieces: inboundQuantity * (detail.QUP || 1),
        QUP: detail.QUP || 1,
        // 保存扁平化数据，用于后续入库操作
        flatItems: detail.flatItems || [],
        transportation_id: detail.transportation_id,
        series_number: detail.series_number,
        // 初始最大可入库数量设置为当前入库数量，后续会在updateCartData中重新计算
        max_quantity: inboundQuantity,
      };
      newCart = [...inboundCart, cartItem];
    }

    // 更新明细的剩余数量
    const updatedDetails = arrivalDetails.map((item, index) => {
      if (index === detailIndex) {
        return {
          ...item,
          remainingQuantity: item.remainingQuantity - inboundQuantity,
          selectedQuantity: 0,
        };
      }
      return item;
    });

    // 计算购物车统计和分组
    this.updateCartData(newCart);

    this.setData({
      inboundCart: newCart,
      arrivalDetails: updatedDetails,
      showConfirmButton: this.checkAllItemsInCart(updatedDetails),
    });

    wx.showToast({
      title: "已添加",
      icon: "success",
      duration: 500,
    });
  },

  /**
   * 更新购物车数据和分组
   */
  updateCartData(cart) {
    // 重新计算每个购物车项的最大可入库数量
    const updatedCart = cart.map((item) => {
      // 获取该明细项的原始包裹数量
      const { arrivalDetails } = this.data;
      const detail = arrivalDetails[item.detail_index];
      if (!detail) return item;

      // 计算该明细项在购物车中其他项的已入库数量（排除当前项）
      const otherInboundQuantity = cart
        .filter(
          (cartItem) =>
            cartItem.detail_index === item.detail_index &&
            cartItem.cart_key !== item.cart_key
        )
        .reduce((sum, cartItem) => sum + cartItem.inbound_quantity, 0);

      // 步进器的最大值 = 原始包裹数量 - 其他项已入库数量
      const maxQuantity = Math.max(
        1,
        detail.package_quantity - otherInboundQuantity
      );

      return {
        ...item,
        max_quantity: maxQuantity,
      };
    });

    // 计算购物车统计
    const cartTotalPackages = updatedCart.reduce(
      (sum, item) => sum + item.inbound_quantity,
      0
    );
    const cartTotalPieces = updatedCart.reduce(
      (sum, item) => sum + item.total_inbound_pieces,
      0
    );

    // 按仓库分组
    const grouped = {};
    updatedCart.forEach((item) => {
      const warehouseId = item.warehouse_id;
      if (!grouped[warehouseId]) {
        grouped[warehouseId] = {
          warehouse_id: warehouseId,
          warehouse_name: item.warehouse_name,
          items: [],
          total_packages: 0,
          total_pieces: 0,
        };
      }
      grouped[warehouseId].items.push(item);
      grouped[warehouseId].total_packages += item.inbound_quantity;
      grouped[warehouseId].total_pieces += item.total_inbound_pieces;
    });

    const groupedCart = Object.values(grouped);

    this.setData({
      cartTotalPackages,
      cartTotalPieces,
      groupedCart,
      inboundCart: updatedCart,
    });
  },

  /**
   * 检查是否所有明细都已预入库
   */
  checkAllItemsInCart(details) {
    const arrivalDetails = details || this.data.arrivalDetails;

    // 检查是否所有明细的剩余数量都为0
    return arrivalDetails.every((detail) => detail.remainingQuantity <= 0);
  },

  /**
   * 购物车中数量变化
   */
  onCartQuantityChange(e) {
    const { cartKey } = e.currentTarget.dataset;
    const newQuantity = parseInt(e.detail) || 0;

    const { inboundCart, arrivalDetails } = this.data;

    // 找到对应的购物车项
    const cartItemIndex = inboundCart.findIndex(
      (item) => item.cart_key === cartKey
    );

    if (cartItemIndex === -1) return;

    const cartItem = inboundCart[cartItemIndex];
    const oldQuantity = cartItem.inbound_quantity;
    const quantityDiff = newQuantity - oldQuantity;

    // 验证新数量是否超过最大可入库数量
    const maxQuantity = this.calculateMaxQuantityForDetail(
      cartItem.detail_index,
      cartKey
    );
    if (newQuantity > maxQuantity + oldQuantity) {
      wx.showToast({
        title: `最多只能入库${maxQuantity + oldQuantity}包`,
        icon: "none",
      });
      return;
    }

    // 更新购物车项
    const newCart = inboundCart.map((item, index) => {
      if (index === cartItemIndex) {
        return {
          ...item,
          inbound_quantity: newQuantity,
          total_inbound_pieces: newQuantity * (item.QUP || 1),
        };
      }
      return item;
    });

    // 更新明细的剩余数量 - 使用detail_index直接定位
    const updatedDetails = arrivalDetails.map((detail, index) => {
      if (index === cartItem.detail_index) {
        const newRemainingQuantity = detail.remainingQuantity - quantityDiff;
        return {
          ...detail,
          remainingQuantity: Math.max(0, newRemainingQuantity), // 确保不会小于0
        };
      }
      return detail;
    });

    // 先设置 arrivalDetails，再调用 updateCartData
    this.setData({
      arrivalDetails: updatedDetails,
    });

    this.updateCartData(newCart);

    this.setData({
      showConfirmButton: this.checkAllItemsInCart(updatedDetails),
    });
  },

  /**
   * 从入库车中移除
   */
  removeFromCart(e) {
    const { cartKey } = e.currentTarget.dataset;
    const { inboundCart, arrivalDetails } = this.data;

    // 找到要删除的项
    const cartItemIndex = inboundCart.findIndex(
      (item) => item.cart_key === cartKey
    );

    if (cartItemIndex === -1) return;

    const cartItem = inboundCart[cartItemIndex];
    const removedQuantity = cartItem.inbound_quantity;

    // 从购物车中移除
    const newCart = inboundCart.filter((_, index) => index !== cartItemIndex);

    // 恢复明细的剩余数量 - 使用detail_index直接定位
    const updatedDetails = arrivalDetails.map((detail, index) => {
      if (index === cartItem.detail_index) {
        return {
          ...detail,
          remainingQuantity: detail.remainingQuantity + removedQuantity,
        };
      }
      return detail;
    });

    this.updateCartData(newCart);

    this.setData({
      inboundCart: newCart,
      arrivalDetails: updatedDetails,
      showCart: newCart.length > 0,
      showConfirmButton: this.checkAllItemsInCart(updatedDetails),
    });
  },

  /**
   * 切换购物车显示
   */
  toggleCart() {
    this.setData({
      showCart: !this.data.showCart,
    });
  },

  /**
   * 清空购物车
   */
  clearCart() {
    wx.showModal({
      title: "确认清空",
      content: "确定要清空入库清单吗？",
      success: (res) => {
        if (res.confirm) {
          // 重新加载货运详情以获取正确的剩余数量
          this.loadTransportationDetail(this.data.transportationInfo.transportation_id);

          this.setData({
            inboundCart: [],
            groupedCart: [],
            cartTotalPackages: 0,
            cartTotalPieces: 0,
            showCart: false,
            showConfirmButton: false,
          });

          wx.showToast({
            title: "已清空入库清单",
            icon: "success",
          });
        }
      },
    });
  },

  /**
   * 核对入库
   */
  async confirmInbound() {
    const { inboundCart, transportationInfo } = this.data;

    if (inboundCart.length === 0) {
      wx.showToast({
        title: "请先添加入库商品",
        icon: "none",
      });
      return;
    }

    try {
      // 查询已入库数据
      const alreadyInboundData = await this.getAlreadyInboundData(
        transportationInfo.transportation_id
      );
      // 跳转到入库确认页面
      const orderData = {
        transportation_id: transportationInfo.transportation_id,
        transportation_info: transportationInfo,
        inbound_items: inboundCart,
        total_packages: inboundCart.reduce(
          (sum, item) => sum + item.inbound_quantity,
          0
        ),
        total_pieces: inboundCart.reduce(
          (sum, item) => sum + item.total_inbound_pieces,
          0
        ),
        // 添加已入库数据
        already_inbound_packages: alreadyInboundData.packages || 0,
        already_inbound_pieces: alreadyInboundData.pieces || 0,
      };

      wx.navigateTo({
        url: `/pages/inbound-confirm/inbound-confirm?orderData=${encodeURIComponent(
          JSON.stringify(orderData)
        )}`,
      });
    } catch (error) {
      console.error("查询已入库数据失败:", error);
      wx.showToast({
        title: "查询已入库数据失败",
        icon: "none",
      });
    }
  },

  /**
   * 获取已入库数据 - 使用新的仓库管理系统API
   */
  async getAlreadyInboundData(transportationId) {
    try {
      // 使用新的统计API查询已入库数据
      const response = await Api.getInboundStatistics({
        transportation_id: transportationId,
      });

      if (response.data && response.data.code === 200) {
        const data = response.data.data || {};
        return {
          packages: data.packages || 0,
          pieces: data.pieces || 0,
        };
      }

      return { packages: 0, pieces: 0 };
    } catch (error) {
      console.error("查询已入库数据失败:", error);
      // 如果新API失败，返回默认值
      return { packages: 0, pieces: 0 };
    }
  },

  /**
   * 一键全入库
   */
  onInboundAllTap() {
    const { selectedWarehouse, arrivalDetails } = this.data;

    if (!selectedWarehouse) {
      wx.showToast({
        title: "请先选择仓库",
        icon: "none",
      });
      return;
    }

    // 检查是否有可入库的明细
    const availableDetails = arrivalDetails.filter(detail => detail.remainingQuantity > 0);

    if (availableDetails.length === 0) {
      wx.showToast({
        title: "没有可入库的明细",
        icon: "none",
      });
      return;
    }

    wx.showModal({
      title: "确认一键全入库",
      content: `确定要将所有明细（${availableDetails.length}项）全部入库到"${selectedWarehouse.name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performBatchInbound(availableDetails);
        }
      },
    });
  },

  /**
   * 执行批量入库
   */
  performBatchInbound(availableDetails) {
    const { selectedWarehouse, inboundCart } = this.data;
    let newCart = [...inboundCart];

    // 遍历所有可入库的明细
    availableDetails.forEach((detail) => {
      // 找到原始明细的索引
      const originalIndex = this.data.arrivalDetails.findIndex(
        item => item.transportation_id === detail.transportation_id &&
                item.series_number === detail.series_number
      );

      if (originalIndex === -1) return;

      const inboundQuantity = detail.remainingQuantity;
      if (inboundQuantity <= 0) return;

      // 生成唯一的购物车项标识
      const cartKey = `${selectedWarehouse.warehouse_id}_${originalIndex}`;

      // 检查是否已经在购物车中
      const existingIndex = newCart.findIndex(item => item.cart_key === cartKey);

      if (existingIndex >= 0) {
        // 更新现有项的数量
        newCart[existingIndex] = {
          ...newCart[existingIndex],
          inbound_quantity: newCart[existingIndex].inbound_quantity + inboundQuantity,
          total_inbound_pieces: (newCart[existingIndex].inbound_quantity + inboundQuantity) * (detail.QUP || 1),
        };
      } else {
        // 添加新项
        const cartItem = {
          cart_key: cartKey,
          detail_index: originalIndex,
          clothing_name: detail.flatItems && detail.flatItems.length > 0
            ? detail.flatItems.map((item) => item.clothing_name).join("、")
            : "未知服装",
          clothing_id: detail.flatItems && detail.flatItems[0]
            ? detail.flatItems[0].clothing_id
            : "",
          warehouse_id: selectedWarehouse.warehouse_id,
          warehouse_name: selectedWarehouse.name,
          inbound_quantity: inboundQuantity,
          total_inbound_pieces: inboundQuantity * (detail.QUP || 1),
          QUP: detail.QUP || 1,
          flatItems: detail.flatItems || [],
          transportation_id: detail.transportation_id,
          series_number: detail.series_number,
          max_quantity: inboundQuantity,
        };
        newCart.push(cartItem);
      }
    });

    // 更新明细的剩余数量
    const updatedDetails = this.data.arrivalDetails.map((item) => {
      const availableDetail = availableDetails.find(
        detail => detail.transportation_id === item.transportation_id &&
                 detail.series_number === item.series_number
      );

      if (availableDetail) {
        return {
          ...item,
          remainingQuantity: 0,
          selectedQuantity: 0,
        };
      }
      return item;
    });

    // 更新购物车数据和分组
    this.updateCartData(newCart);

    this.setData({
      inboundCart: newCart,
      arrivalDetails: updatedDetails,
      showConfirmButton: this.checkAllItemsInCart(updatedDetails),
    });

    wx.showToast({
      title: `已添加${availableDetails.length}项到入库清单`,
      icon: "success",
      duration: 1500,
    });
  },

  /**
   * 新增仓库
   */
  onAddWarehouse() {
    wx.navigateTo({
      url: "/pages/warehouse-detail/warehouse-detail",
    });
  },

  /**
   * 编辑仓库
   */
  onEditWarehouse(e) {
    // 在微信小程序中阻止事件冒泡的正确方法
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }

    const { warehouse } = e.currentTarget.dataset;

    // 将仓库数据传递给详情页面
    const warehouseData = {
      id: warehouse.warehouse_id,
      name: warehouse.name,
      address: warehouse.address,
      capacity: warehouse.capacity || "",
      rentPrice: warehouse.rent_price || "",
      rentDate: warehouse.rent_date || "",
      returnDate: warehouse.return_date || "",
    };

    wx.navigateTo({
      url: `/pages/warehouse-detail/warehouse-detail?warehouseData=${encodeURIComponent(
        JSON.stringify(warehouseData)
      )}`,
    });
  },

  /**
   * 点击服装名称显示服装信息
   */
  async onClothingNameTap(e) {
    const { clothingInfo, isOem } = e.currentTarget.dataset;

    if (!clothingInfo || !clothingInfo.clothing_id) {
      wx.showToast({
        title: "服装信息不完整",
        icon: "none",
      });
      return;
    }

    try {
      let detailInfo;
      // 判断是否为OEM服装：检查oem字段或isOem参数
      const isOemClothing =
        isOem || clothingInfo.oem === "是" || clothingInfo.oem === "yes";

      if (isOemClothing) {
        // 获取OEM服装详细信息
        const response = await Api.getOemClothingInfo({
          oem_clothing_id: clothingInfo.clothing_id,
        });
        console.log("OEM服装API响应:", response);
        if (response.data) {
          detailInfo = response.data;
        }
      } else {
        // 获取普通服装详细信息
        const response = await Api.getClothingInfo({
          clothing_id: clothingInfo.clothing_id,
        });
        console.log("普通服装API响应:", response);
        if (response.data) {
          detailInfo = response.data;
        }
      }

      if (detailInfo) {
        this.setData({
          selectedClothingInfo: detailInfo,
          isOemClothing: isOemClothing,
          showClothingInfo: true,
        });
      } else {
        wx.showToast({
          title: "获取服装信息失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("获取服装信息失败:", error);
      wx.showToast({
        title: "获取服装信息失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 关闭服装信息弹窗
   */
  onCloseClothingInfo() {
    this.setData({
      showClothingInfo: false,
      selectedClothingInfo: null,
      isOemClothing: false,
    });
  },
});
